/** @type {import('dependency-cruiser').IConfiguration} */

const baseRegex = '^src';

const domainRulesExceptions = [
  `${baseRegex}/domain/services/CryptoService.ts`,
  `${baseRegex}/domain/services/RandomIdGenerator.ts`,
  `${baseRegex}/domain/value-objects/Date.ts`,
  `${baseRegex}/domain/value-objects/Money.ts`,
  `${baseRegex}/domain/value-objects/Number.ts`,
  `${baseRegex}/domain/value-objects/PhoneNumber.ts`,
  `${baseRegex}/domain/value-objects/String.ts`,
  `${baseRegex}/domain/value-objects/UUID.ts`,
];

const domainRulesExceptionsPathNot = domainRulesExceptions.length > 0
  ? `(${domainRulesExceptions.join('|')})`
  : `$baseRegex`;

module.exports = {
  forbidden: [
    {
      name: 'domain-no-external-deps',
      severity: 'error',
      comment: 'The domain layer must not depend on external modules, except for the allowed Value Objects.',
      from: {
        path: `${baseRegex}/domain`,
        pathNot: domainRulesExceptionsPathNot
      },
      to: {
        path: '^node_modules',
        pathNot: '^node_modules/@discocil/fv-domain-library/dist/src/domain'
      }
    },
    {
      name: 'no-config-in-domain',
      severity: 'error',
      comment: 'La capa de dominio no debería depender de configuraciones globales.',
      from: {
        path: `${baseRegex}/domain`,
        pathNot: domainRulesExceptionsPathNot
      },
      to: {
        path: '^config'
      }
    },
    {
      name: 'domain-no-deps-on-applications-or-infrastructure',
      severity: 'error',
      comment: 'The domain layer must not depend on application or infrastructure layers.',
      from: {
        path: `${baseRegex}/domain`,
        pathNot: domainRulesExceptionsPathNot
      },
      to: {
        path: `${baseRegex}/(application|infrastructure)`
      }
    },
    {
      name: 'application-no-deps-on-infrastructure',
      severity: 'error',
      comment: 'The application layer must not depend on infrastructure, only on the domain.',
      from: {
        path: `${baseRegex}/application`
      },
      to: {
        path: `${baseRegex}/infrastructure`
      }
    },
    {
      name: 'infra-no-circular-to-domain',
      severity: 'error',
      comment: 'The infrastructure layer must not have cyclic dependencies with domain or application.',
      from: {
        path: `${baseRegex}/infrastructure`
      },
      to: {
        circular: true,
        path: `${baseRegex}/(domain|application)`
      }
    },
    {
      name: 'domain-no-deprecated-core',
      severity: 'error',
      comment: 'The domain must not depend on deprecated or node.js specific core modules.',
      from: {
        path: `${baseRegex}/domain`
      },
      to: {
        dependencyTypes: ['core']
      }
    },
    {
      name: 'application-only-deps-on-domain',
      severity: 'error',
      comment: 'The application layer should only depend on the domain directly, not on external modules or infrastructure.',
      from: {
        path: `${baseRegex}/application`
      },
      to: {
        pathNot: `${baseRegex}/(application|domain)|node_modules`
      }
    },
    {
      name: 'no-unresolved-in-domain',
      severity: 'error',
      comment: 'All domain layer dependencies must be resolved correctly.',
      from: {
        path: `${baseRegex}/domain`
      },
      to: {
        couldNotResolve: true
      }
    },
    {
      name: 'no-dev-deps-in-prod-code',
      severity: 'error',
      comment: 'Do not use development dependencies in code intended for productio.',
      from: {
        path: `${baseRegex}/(domain|application|infrastructure)`,
        pathNot: '\\.(spec|test)\\.ts$'
      },
      to: {
        dependencyTypes: ['npm-dev']
      }
    },
    {
      name: 'no-circular',
      severity: 'warn',
      comment:
        'This dependency is part of a circular relationship. You might want to revise ' +
        'your solution (i.e. use dependency inversion, make sure the modules have a single responsibility) ',
      from: {},
      to: {
        circular: true
      }
    },
    {
      name: 'no-orphans',
      comment:
        "This is an orphan module - it's likely not used (anymore?). Either use it or " +
        "remove it. If it's logical this module is an orphan (i.e. it's a config file), " +
        "add an exception for it in your dependency-cruiser configuration. By default " +
        "this rule does not scrutinize dot-files (e.g. .eslintrc.js), TypeScript declaration " +
        "files (.d.ts), tsconfig.json and some of the babel and webpack configs.",
      severity: 'warn',
      from: {
        orphan: true,
        pathNot: [
          '(^|/)\.[^/]+\.(js|cjs|mjs|ts|json)$',
          '\.d\.ts$',
          '(^|/)tsconfig\.json$',
          '(^|/)(babel|webpack)\.config\.(js|cjs|mjs|ts|json)$'
        ]
      },
      to: {},
    },
    {
      name: 'no-deprecated-core',
      comment:
        "A module depends on a node core module that has been deprecated. Find an alternative - these are " +
        "bound to exist - node doesn't deprecate lightly.",
      severity: 'warn',
      from: {},
      to: {
        dependencyTypes: [
          'core'
        ],
        path: [
          '^(v8/tools/codemap)$',
          '^(v8/tools/consarray)$',
          '^(v8/tools/csvparser)$',
          '^(v8/tools/logreader)$',
          '^(v8/tools/profile_view)$',
          '^(v8/tools/profile)$',
          '^(v8/tools/SourceMap)$',
          '^(v8/tools/splaytree)$',
          '^(v8/tools/tickprocessor-driver)$',
          '^(v8/tools/tickprocessor)$',
          '^(node-inspect/lib/_inspect)$',
          '^(node-inspect/lib/internal/inspect_client)$',
          '^(node-inspect/lib/internal/inspect_repl)$',
          '^(async_hooks)$',
          '^(punycode)$',
          '^(domain)$',
          '^(constants)$',
          '^(sys)$',
          '^(_linklist)$',
          '^(_stream_wrap)$'
        ],
      }
    },
    {
      name: 'not-to-deprecated',
      comment:
        'This module uses a (version of an) npm module that has been deprecated. Either upgrade to a later ' +
        'version of that module, or find an alternative. Deprecated modules are a security risk.',
      severity: 'warn',
      from: {},
      to: {
        dependencyTypes: [
          'deprecated'
        ]
      }
    },
    {
      name: 'no-non-package-json',
      severity: 'error',
      comment:
        "This module depends on an npm package that isn't in the 'dependencies' section of your package.json. " +
        "That's problematic as the package either (1) won't be available on live (2 - worse) will be " +
        "available on live with an non-guaranteed version. Fix it by adding the package to the dependencies " +
        "in your package.json.",
      from: {},
      to: {
        dependencyTypes: [
          'npm-no-pkg',
          'npm-unknown'
        ]
      }
    },
    {
      name: 'not-to-unresolvable',
      comment:
        "This module depends on a module that cannot be found ('resolved to disk'). If it's an npm " +
        "module: add it to your package.json. In all other cases you likely already know what to do.",
      severity: 'error',
      from: {},
      to: {
        couldNotResolve: true
      }
    },
    {
      name: 'no-duplicate-dep-types',
      comment:
        "Likely this module depends on an external ('npm') package that occurs more than once " +
        "in your package.json i.e. bot as a devDependencies and in dependencies. This will cause " +
        "maintenance problems later on.",
      severity: 'warn',
      from: {},
      to: {
        moreThanOneDependencyType: true,
        dependencyTypesNot: ['type-only']
      }
    },

    /* rules you might want to tweak for your specific situation: */
    {
      name: 'not-to-test',
      comment:
        "This module depends on code within a folder that should only contain tests. As tests don't " +
        "implement functionality this is odd. Either you're writing a test outside the test folder " +
        "or there's something in the test folder that isn't a test.",
      severity: 'error',
      from: {
        pathNot: '^(tests)'
      },
      to: {
        path: '^(tests)'
      }
    },
    {
      name: 'not-to-spec',
      comment:
        "This module depends on a spec (test) file. The sole responsibility of a spec file is to test code. " +
        "If there's something in a spec that's of use to other modules, it doesn't have that single " +
        "responsibility anymore. Factor it out into (e.g.) a separate utility/ helper or a mock.",
      severity: 'error',
      from: {},
      to: {
        path: '\.(spec|test)\.(js|mjs|cjs|ts|ls|coffee|litcoffee|coffee\.md)$'
      }
    },
    {
      name: 'not-to-dev-dep',
      severity: 'error',
      comment:
        "This module depends on an npm package from the 'devDependencies' section of your " +
        "package.json. It looks like something that ships to production, though. To prevent problems " +
        "with npm packages that aren't there on production declare it (only!) in the 'dependencies'" +
        "section of your package.json. If this module is development only - add it to the " +
        "from.pathNot re of the not-to-dev-dep rule in the dependency-cruiser configuration",
      from: {
        path: '^(src)',
        pathNot: '\.(spec|test)\.(js|mjs|cjs|ts|ls|coffee|litcoffee|coffee\.md)$'
      },
      to: {
        dependencyTypes: [
          'npm-dev'
        ]
      }
    },
    {
      name: 'optional-deps-used',
      severity: 'info',
      comment:
        "This module depends on an npm package that is declared as an optional dependency " +
        "in your package.json. As this makes sense in limited situations only, it's flagged here. " +
        "If you're using an optional dependency here by design - add an exception to your" +
        "dependency-cruiser configuration.",
      from: {},
      to: {
        dependencyTypes: [
          'npm-optional'
        ]
      }
    },
    {
      name: 'peer-deps-used',
      comment:
        "This module depends on an npm package that is declared as a peer dependency " +
        "in your package.json. This makes sense if your package is e.g. a plugin, but in " +
        "other cases - maybe not so much. If the use of a peer dependency is intentional " +
        "add an exception to your dependency-cruiser configuration.",
      severity: 'warn',
      from: {},
      to: {
        dependencyTypes: [
          'npm-peer'
        ]
      }
    }
  ],
  options: {
    doNotFollow: {
      path: 'node_modules'
    },
    tsPreCompilationDeps: true,
    tsConfig: {
      fileName: 'tsconfig.json'
    },
    enhancedResolveOptions: {
      exportsFields: ['exports'],
      conditionNames: ['import', 'require', 'node', 'default'],
      mainFields: ['main', 'types', 'typings'],
    },
    reporterOptions: {
      dot: {
        collapsePattern: 'src/[^/]+/[^/]+|node_modules/(@[^/]+/[^/]+|[^/]+)'
      },
      archi: {
        collapsePattern: 'src/[^/]+/[^/]+|node_modules/(@[^/]+/[^/]+|[^/]+)'
      },
      text: {
        highlightFocused: true
      },
    }
  }
};
