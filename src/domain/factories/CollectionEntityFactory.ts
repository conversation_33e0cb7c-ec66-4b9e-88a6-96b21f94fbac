import { Collection } from '../collections/Collection';

import type { CollectionKey } from '../collections/Collection';

/**
 * Interface genérica para entidades de colección
 */
export interface ICollectionEntity<T> {
  value: Collection<T>;
  toPrimitives(): T[];
  isEmpty(): boolean;
  length(): number;
}

/**
 * Implementación genérica de entidad de colección
 */
export class GenericCollectionEntity<T> implements ICollectionEntity<T> {
  constructor(public readonly value: Collection<T>) {}

  toPrimitives(): T[] {
    return this.value.toArray();
  }

  isEmpty(): boolean {
    return this.value.isEmpty();
  }

  length(): number {
    return this.value.length();
  }

  add(item: T): this {
    this.value.add(item);

    return this;
  }

  filter(callback: (value: T, index: number, array: T[]) => unknown): Collection<T> {
    return this.value.filter(callback);
  }

  sort(callback?: (a: T, b: T) => number): Collection<T> {
    return this.value.sort(callback);
  }
}

/**
 * Factory para crear entidades de colección tipadas
 */
export class CollectionEntityFactory {
  /**
   * Crea una entidad de colección genérica
   */
  static create<T>(items: T[], key?: CollectionKey<T>): GenericCollectionEntity<T> {
    const collection = Collection.build(items, key);

    return new GenericCollectionEntity(collection);
  }

  /**
   * Crea una entidad de colección vacía
   */
  static createEmpty<T>(key?: CollectionKey<T>): GenericCollectionEntity<T> {
    const collection = Collection.new<T>(key);

    return new GenericCollectionEntity(collection);
  }

  /**
   * Crea una entidad de colección de números
   */
  static createNumbers(numbers: number[]): GenericCollectionEntity<number> {
    return this.create(numbers);
  }

  /**
   * Crea una entidad de colección de strings
   */
  static createStrings(strings: string[]): GenericCollectionEntity<string> {
    return this.create(strings);
  }

  /**
   * Crea una entidad de colección de objetos con clave
   */
  static createObjects<T extends object>(objects: T[], key: CollectionKey<T>): GenericCollectionEntity<T> {
    return this.create(objects, key);
  }
}

/**
 * Tipos de utilidad para diferentes casos de uso
 */
export type NumberCollectionEntity = GenericCollectionEntity<number>;
export type StringCollectionEntity = GenericCollectionEntity<string>;
export type ObjectCollectionEntity<T extends object> = GenericCollectionEntity<T>;

/**
 * Builder pattern para construcción fluida
 */
export class CollectionEntityBuilder<T> {
  private items: T[] = [];
  private key?: CollectionKey<T>;

  static new<T>(): CollectionEntityBuilder<T> {
    return new CollectionEntityBuilder<T>();
  }

  withItems(items: T[]): this {
    this.items = items;

    return this;
  }

  withKey(key: CollectionKey<T>): this {
    this.key = key;

    return this;
  }

  addItem(item: T): this {
    this.items.push(item);

    return this;
  }

  build(): GenericCollectionEntity<T> {
    return CollectionEntityFactory.create(this.items, this.key);
  }
}
