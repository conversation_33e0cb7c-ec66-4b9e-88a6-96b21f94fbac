import { FvError } from '../errors/FvError';

import type { UnknownObject } from '../contracts/Generics';
import type { Either } from '../contracts/Result';

type DescriptorResponse = Either<unknown, unknown>;

export const contextualizeError
  = () =>
    (
      _target: unknown,
      _propertyKey: string,
      descriptor: PropertyDescriptor,
    ): PropertyDescriptor => {
      const originalMethod = descriptor.value;

      descriptor.value = async function (
        ...args: unknown[]
      ): Promise<DescriptorResponse> {
        const originalMethodApplied: DescriptorResponse
        = await originalMethod.apply(this, args);

        if (
          originalMethodApplied?.isLeft
          && originalMethodApplied.isLeft()
          && originalMethodApplied.value instanceof FvError
          && originalMethodApplied.value.isAutoContextualizable
        ) {
          originalMethodApplied.value.contextualize({
            context: this.constructor.name,
            data: args as unknown as UnknownObject,
          });
        }

        return originalMethodApplied;
      };

      return descriptor;
    };
