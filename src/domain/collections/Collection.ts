import { Maybe } from '../contracts/Maybe';
import { FvObject } from '../value-objects/Object';
import { FvString } from '../value-objects/String';

export type CollectionKey<T> = T extends object ? keyof T : string | number;
export type Item<T> = T[] | Set<T>;

export class Collection<T> {
  protected readonly items = new Map<string, T>();

  private cachedArray: T[] = [];

  protected constructor(protected readonly key?: CollectionKey<T>) {}

  static new<T>(key?: CollectionKey<T>): Collection<T> {
    return new this(key);
  }

  static build<T>(items: Item<T>, key?: CollectionKey<T>): Collection<T> {
    const collection = Collection.new<T>(key);

    items.forEach(item => collection.add(item));

    return collection;
  }

  private getKey(value: T): string {
    const isObjectValid = this.key && FvString.is(this.key) && FvObject.is(value);

    return String(isObjectValid ? value[this.key] : value);
  }

  length(): number {
    return this.items.size;
  }

  isEmpty(): boolean {
    return this.items.size === 0;
  }

  isNotEmpty(): boolean {
    return !this.isEmpty();
  }

  clear(): this {
    this.items.clear();

    return this;
  }

  add(item: T): this {
    this.cachedArray = [];

    const mapKey = this.getKey(item);

    this.items.set(mapKey, item);

    return this;
  }

  contains(index: string | number): boolean {
    return this.items.has(index.toString());
  }

  removeByIndex(index: string | number): this {
    const stringIndex = index.toString();

    if (!this.items.has(stringIndex)) {
      return this;
    }

    this.cachedArray = [];

    this.items.delete(stringIndex);

    return this;
  }

  removeByValue(value: T): this {
    for (const [index, item] of this.items.entries()) {
      if (Object.is(item, value)) {
        this.items.delete(index);

        return this;
      }
    }

    return this;
  }

  getByIndex(index: string | number): Maybe<T> {
    return Maybe.fromValue(this.items.get(index.toString()));
  }

  getByValue(value: T): Maybe<T> {
    const mapKey = this.getKey(value);

    return this.getByIndex(mapKey);
  }

  uniques(): Collection<T> {
    const values = this.items.values();
    const uniques = new Set(values);

    return Collection.build(Array.from(uniques), this.key);
  }

  toArray(): T[] {
    if (this.cachedArray.length === 0) {
      this.cachedArray = Array.from(this.items.values());
    }

    return this.cachedArray;
  }

  merge(collection: Collection<T>): this {
    for (const itemCollection of collection) {
      this.add(itemCollection);
    }

    return this;
  }

  intersection(collection: Collection<T>): this {
    const setA = new Set(collection.toArray());
    const setB = new Set(this.toArray());
    const intersection = setA.intersection(setB);

    return Collection.build<T>(Array.from(intersection), this.key) as unknown as this;
  }

  filter(callback: (value: T, index: number, array: T[]) => unknown): Collection<T> {
    const filteredItems = this.toArray().filter(callback);

    return Collection.build(Array.from(filteredItems), this.key);
  }

  some(callback: (value: T, index: number, array: T[]) => unknown): boolean {
    return this.toArray().some(callback);
  }

  sort(callback?: (a: T, b: T) => number): Collection<T> {
    const sortedItems = this.toArray().toSorted(callback);

    return Collection.build(Array.from(sortedItems), this.key);
  }

  countOccurrences(valueToFind: T): number {
    let countOccurrences = 0;

    for (const collectionItem of this.items.values()) {
      if (JSON.stringify(collectionItem) === JSON.stringify(valueToFind)) {
        countOccurrences++;
      }
    }

    return countOccurrences;
  }

  first(): Maybe<T> {
    const iterator = this.items.values();
    const first = iterator.next().value;

    return Maybe.fromValue(first);
  }

  last(): Maybe<T> {
    return Maybe.fromValue(this.toArray().at(-1));
  }

  *[Symbol.iterator](): Generator<T> {
    yield * this.items.values();
  }
}
