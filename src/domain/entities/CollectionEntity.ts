import { Collection, CollectionKey } from '../collections/Collection';

/**
 * Clase base abstracta para entidades que manejan colecciones
 */
export abstract class CollectionEntity<T> {
  protected readonly _value: Collection<T>;

  constructor(value: Collection<T>) {
    this._value = value;
  }

  /**
   * Obtiene la colección
   */
  get value(): Collection<T> {
    return this._value;
  }

  /**
   * Convierte la entidad a primitivos
   */
  toPrimitives(): T[] {
    return this._value.toArray();
  }

  /**
   * Obtiene la colección como array
   */
  toArray(): T[] {
    return this._value.toArray();
  }

  /**
   * Verifica si la colección está vacía
   */
  isEmpty(): boolean {
    return this._value.isEmpty();
  }

  /**
   * Obtiene la longitud de la colección
   */
  length(): number {
    return this._value.length();
  }

  /**
   * Agrega un elemento a la colección
   */
  add(item: T): this {
    this._value.add(item);

    return this;
  }

  /**
   * Filtra la colección
   */
  filter(callback: (value: T, index: number, array: T[]) => unknown): Collection<T> {
    return this._value.filter(callback);
  }

  /**
   * Ordena la colección
   */
  sort(callback?: (a: T, b: T) => number): Collection<T> {
    return this._value.sort(callback);
  }
}

/**
 * Implementación concreta para números
 */
export class NumberCollectionEntity extends CollectionEntity<number> {
  static create(numbers: number[]): NumberCollectionEntity {
    return new NumberCollectionEntity(Collection.build(numbers));
  }

  /**
   * Suma todos los números de la colección
   */
  sum(): number {
    return this._value.toArray().reduce((acc, num) => acc + num, 0);
  }

  /**
   * Obtiene el promedio
   */
  average(): number {
    const array = this._value.toArray();

    return array.length > 0 ? this.sum() / array.length : 0;
  }
}

/**
 * Implementación concreta para strings
 */
export class StringCollectionEntity extends CollectionEntity<string> {
  static create(strings: string[]): StringCollectionEntity {
    return new StringCollectionEntity(Collection.build(strings));
  }

  /**
   * Concatena todos los strings
   */
  join(separator: string = ''): string {
    return this._value.toArray().join(separator);
  }

  /**
   * Filtra por strings que contengan un texto
   */
  filterByText(text: string): Collection<string> {
    return this._value.filter(str => str.includes(text));
  }
}

/**
 * Implementación concreta para objetos con ID
 */
export class ObjectCollectionEntity<T extends { id: string | number; }> extends CollectionEntity<T> {
  constructor(value: Collection<T>) {
    super(value);
  }

  static create<T extends { id: string | number; }>(objects: T[]): ObjectCollectionEntity<T> {
    return new ObjectCollectionEntity(Collection.build(objects, 'id'));
  }

  /**
   * Busca por ID
   */
  findById(id: string | number): T | undefined {
    return this._value.getByIndex(id).fold(() => undefined, value => value);
  }

  /**
   * Obtiene todos los IDs
   */
  getIds(): (string | number)[] {
    return this._value.toArray().map(item => item.id);
  }
}
