import { Entity } from './Entity';

import type { DomainEvent } from '../events/DomainEvent';

export abstract class AggregateRoot extends Entity {
  private domainEvents: DomainEvent[] = [];

  pullDomainEvents(): DomainEvent[] {
    const domainEvents = this.getDomainEvents();

    this.clearEvents();

    return domainEvents;
  }

  getDomainEvents(): DomainEvent[] {
    return this.domainEvents.slice();
  }

  protected addDomainEvent(domainEvent: DomainEvent): void {
    this.domainEvents.push(domainEvent);
  }

  private clearEvents(): void {
    this.domainEvents = [];
  }
}
