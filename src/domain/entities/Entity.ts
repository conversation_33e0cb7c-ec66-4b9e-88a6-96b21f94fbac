import { FvString } from '../value-objects/String';
import { UniqueEntityID } from '../value-objects/UniqueEntityID';

export abstract class Entity {
  protected constructor(protected readonly _id: UniqueEntityID) { }

  get id(): string {
    return this._id.value;
  }

  equalTo(other: Entity | UniqueEntityID | string): boolean {
    if (other instanceof Entity) {
      return FvString.build(this.id).isEqualTo(other.id);
    }

    if (other instanceof UniqueEntityID) {
      return this._id.equals(other);
    }

    return FvString.build(other).isEqualTo(this.id);
  }
}
