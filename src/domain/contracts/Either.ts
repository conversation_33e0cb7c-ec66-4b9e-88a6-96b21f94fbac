enum EEitherKind {
  LEFT = 'left',
  RIGHT = 'right'
}

type Left<L> = {
  kind: EEitherKind.LEFT;
  value: L;
};

type Right<R> = {
  kind: EEitherKind.RIGHT;
  value: R;
};

type EitherValue<L, R> = Left<L> | Right<R>;

export class Either<L, R> {
  private constructor(private readonly value: EitherValue<L, R>) {}

  isLeft(): this is Left<L> {
    return this.value.kind === EEitherKind.LEFT;
  }

  isRight(): this is Right<R> {
    return this.value.kind === EEitherKind.RIGHT;
  }

  get(): R {
    return this.getOrThrow();
  }

  getOrElse(defaultValue: R): R {
    return this.fold(
      () => defaultValue,
      someValue => someValue,
    );
  }

  getOrThrow(errorMessage?: string): R {
    const throwFn = (): never => {
      throw new Error(errorMessage || `An error has ocurred: ${JSON.stringify(this.value.value)}`);
    };

    return this.fold(
      () => throwFn(),
      value => value,
    );
  }

  map<T>(fn: (value: R) => T): Either<L, T> {
    return this.flatMap(value => Either.right<L, T>(fn(value)));
  }

  flatMap<T>(fn: (right: R) => Either<L, T>): Either<L, T> {
    return this.fold(
      value => Either.left(value),
      value => fn(value),
    );
  }

  fold<T>(leftFn: (left: L) => T, rightFn: (right: R) => T): T {
    if (this.value.kind === EEitherKind.LEFT) {
      return leftFn(this.value.value);
    }

    return rightFn(this.value.value);
  }

  static left<L, R>(value: L): Either<L, R> {
    return new Either<L, R>({ kind: EEitherKind.LEFT, value });
  }

  static right<L, R>(value: R): Either<L, R> {
    return new Either<L, R>({ kind: EEitherKind.RIGHT, value });
  }
}
