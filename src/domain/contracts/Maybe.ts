enum EMaybeKind {
  NONE = 'none',
  SOME = 'some'
}

type None = {
  kind: EMaybeKind.NONE;
};

type Some<Data> = {
  kind: EMaybeKind.SOME;
  someValue: Data;
};

type MaybeValue<Data> = None | Some<Data>;

const isNull = (value: unknown): value is null => value === null;
const isUndefined = (value: unknown): value is undefined => value === undefined;
const isEmptyString = (value: unknown): value is string => value === '';
const isEmpty = (value: unknown): value is null | undefined => isNull(value) || isUndefined(value) || isEmptyString(value);

export class Maybe<Data> {
  private constructor(private readonly value: MaybeValue<Data>) {}

  isEmpty(): this is None {
    return this.value.kind === EMaybeKind.NONE;
  }

  isDefined(): this is Some<Data> {
    return this.value.kind === EMaybeKind.SOME;
  }

  is(value: Data): this is Some<Data> {
    if (this.isEmpty()) {
      return false;
    }

    return this.get() === value;
  }

  get(): Data {
    return this.getOrThrow();
  }

  getOrThrow(errorMessage?: string): Data {
    const throwFn = (): never => {
      throw new Error(errorMessage || 'Value is empty');
    };

    return this.fold(
      () => throwFn(),
      someValue => someValue,
    );
  }

  getOrElse(defaultValue: Data): Data {
    return this.fold(
      () => defaultValue,
      someValue => someValue,
    );
  }

  map<T>(f: (wrapped: Data) => T): Maybe<T> {
    return this.flatMap(data => Maybe.fromValue(f(data)));
  }

  flatMap<T>(f: (wrapped: Data) => Maybe<T>): Maybe<T> {
    return this.fold(
      () => Maybe.none(),
      someValue => f(someValue),
    );
  }

  fold<T>(leftFn: () => T, rightFn: (someValue: Data) => T): T {
    if (this.value.kind === EMaybeKind.NONE) {
      return leftFn();
    }

    return rightFn(this.value.someValue);
  }

  static some<Data>(value: Data): Maybe<Data> {
    if (isEmpty(value)) {
      throw new Error('Provided value must not be empty');
    }

    return new Maybe({ kind: EMaybeKind.SOME, someValue: value });
  }

  static none<Data>(): Maybe<Data> {
    return new Maybe({ kind: EMaybeKind.NONE });
  }

  static fromValue<Data>(value: Data | undefined | null): Maybe<Data> {
    return isEmpty(value) ? Maybe.none() : Maybe.some(value);
  }
}
