import type { FvError } from '../errors/FvError';
import type { UnknownObject } from './Generics';

export type LoggerMessage = string | UnknownObject;
export type LoggerErrorMessage = string | FvError | Error;

export interface Logger {
  debug: (message: LoggerMessage | LoggerErrorMessage, context?: UnknownObject) => void;
  error: (message: LoggerErrorMessage, context?: UnknownObject) => void;
  info: (message: LoggerMessage, context?: UnknownObject) => void;
  log: (message: LoggerMessage, context?: UnknownObject) => void;
  warn: (message: LoggerErrorMessage, context?: UnknownObject) => void;
}

export interface LoggerStrategy {
  debug: (message: string, context?: UnknownObject) => void;
  error: (message: string, context?: UnknownObject) => void;
  info: (message: string, context?: UnknownObject) => void;
  log: (message: string, context?: UnknownObject) => void;
  warn: (message: string, context?: UnknownObject) => void;
}
