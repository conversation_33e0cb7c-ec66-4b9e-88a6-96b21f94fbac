export enum EPaymentsProvider {
  ADYEN = 'adyen',
  ADYEN_PLATFORMS = 'adyen-platforms',
  CECA = 'ceca',
  NUVEI = 'nuvei',
  ZRU = 'zru'
}

export enum EPaymentChannel {
  EXTERNAL = 'external',
  INTERNAL = 'internal'
}

export enum EPaymentMethod {
  BANK_CARD = 'bank_card',
  BANK_TRANSFER = 'bank_transfer',
  CASH = 'cash',
  INSTANT_TRANSFER = 'instant_transfer'
}

export enum EPaymentStates {
  COMPLETED = 'completado',
  ERROR = 'error',
  PENDING = 'pendiente',
  REVERSED = 'revertido'
}

export enum EPaymentType {
  PAYMENT = 'payment',
  REFUND = 'refund'
}
