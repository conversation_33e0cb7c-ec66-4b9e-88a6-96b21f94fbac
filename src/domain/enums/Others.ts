export enum EArtistSource {
  SPOTIFY = 'spotify'
}

export enum ELegacyPassState {
  PENDING = 'pendiente',
  VALID = 'valido',
  REJECTED = 'rechazado'
}

export enum ESubscriptionPlanType {
  MONTHLY = 'mensual',
  ANNUAL = 'anual'
}

export enum EPassTypeSaleTypes {
  ONLINE = 'online',
  PAYLINK = 'paylink'
}

export enum EResourceType {
  TICKETS = 'entradas',
  PASSES = 'passes'
}

export enum EResourceTypes {
  BOOKINGS = 'reservas',
  CASHLESS = 'cashless',
  NAME_CHANGES = 'cambios_nombre',
  PASSES = 'passes',
  PLANS = 'planes',
  RESERVED = 'reservados',
  TICKETS = 'entradas',
  UPSELLING = 'upselling'
}

export enum NotificationTypeEnum {
  SMS = 'sms',
  EMAIL = 'email'
}

export enum ESaleStates {
  ACTIVE = 'activada'
}

export enum ETypeTicketLimit {
  TICKETS = 'entradas',
  BOOKINGS = 'reservas'
}

export enum ETicketRateTypes {
  LIMITED = 'limitada',
  PROMOTION = 'promocion',
  PUBLIC = 'publica'
}

export enum EReceptionistTypes {
  USER = 'usuario',
  BUSINESS = 'negocio'
}

export enum ENameChangeTypes {
  PERMANENT = 'fijo',
  PRICE_DIFFERENCE = 'diferencia_precio'
}

export enum EQuestionTypes {
  DROPDOWN = 'dropdown',
  SELECT = 'select',
  TEXT = 'text'
}

export enum ETicketTypeDisponibilityTypes {
  PERCENTAGE = 'percentage',
  PRESET_AMOUNT = 'preset_amount',
  REAL_AMOUNT = 'real_amount'
}
