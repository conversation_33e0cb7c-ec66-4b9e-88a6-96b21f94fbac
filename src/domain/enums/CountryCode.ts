export enum ECountryCode {
  AF = 'AF',
  AL = 'AL',
  DE = 'DE',
  AD = 'AD',
  AO = 'AO',
  AI = 'AI',
  AQ = 'AQ',
  AG = 'AG',
  SA = 'SA',
  DZ = 'DZ',
  AR = 'AR',
  AM = 'AM',
  AW = 'AW',
  AU = 'AU',
  AT = 'AT',
  AZ = 'AZ',
  BE = 'BE',
  BS = 'BS',
  BH = 'BH',
  BD = 'BD',
  BB = 'BB',
  BZ = 'BZ',
  BJ = 'BJ',
  BT = 'BT',
  BY = 'BY',
  MM = 'MM',
  BO = 'BO',
  BA = 'BA',
  BW = 'BW',
  BR = 'BR',
  BN = 'BN',
  BG = 'BG',
  BF = 'BF',
  BI = 'BI',
  CV = 'CV',
  KH = 'KH',
  CM = 'CM',
  CA = 'CA',
  TD = 'TD',
  CL = 'CL',
  CN = 'CN',
  CY = 'CY',
  VA = 'VA',
  CO = 'CO',
  KM = 'KM',
  CG = 'CG',
  CD = 'CD',
  KP = 'KP',
  KR = 'KR',
  CI = 'CI',
  CR = 'CR',
  HR = 'HR',
  CU = 'CU',
  CW = 'CW',
  DK = 'DK',
  DM = 'DM',
  EC = 'EC',
  EG = 'EG',
  SV = 'SV',
  AE = 'AE',
  ER = 'ER',
  SK = 'SK',
  SI = 'SI',
  ES = 'ES',
  US = 'US',
  EE = 'EE',
  ET = 'ET',
  PH = 'PH',
  FI = 'FI',
  FJ = 'FJ',
  FR = 'FR',
  GA = 'GA',
  GM = 'GM',
  GE = 'GE',
  GH = 'GH',
  GI = 'GI',
  GD = 'GD',
  GR = 'GR',
  GL = 'GL',
  GP = 'GP',
  GU = 'GU',
  GT = 'GT',
  GF = 'GF',
  GG = 'GG',
  GN = 'GN',
  GQ = 'GQ',
  GW = 'GW',
  GY = 'GY',
  HT = 'HT',
  HN = 'HN',
  HK = 'HK',
  HU = 'HU',
  IN = 'IN',
  ID = 'ID',
  IR = 'IR',
  IQ = 'IQ',
  IE = 'IE',
  BV = 'BV',
  IM = 'IM',
  CX = 'CX',
  NF = 'NF',
  IS = 'IS',
  BM = 'BM',
  KY = 'KY',
  CC = 'CC',
  CK = 'CK',
  AX = 'AX',
  FO = 'FO',
  GS = 'GS',
  HM = 'HM',
  MV = 'MV',
  FK = 'FK',
  MP = 'MP',
  MH = 'MH',
  PN = 'PN',
  SB = 'SB',
  TC = 'TC',
  UM = 'UM',
  VG = 'VG',
  VI = 'VI',
  IL = 'IL',
  IT = 'IT',
  JM = 'JM',
  JP = 'JP',
  JE = 'JE',
  JO = 'JO',
  KZ = 'KZ',
  KE = 'KE',
  KG = 'KG',
  KI = 'KI',
  KW = 'KW',
  LB = 'LB',
  LA = 'LA',
  LS = 'LS',
  LV = 'LV',
  LR = 'LR',
  LY = 'LY',
  LI = 'LI',
  LT = 'LT',
  LU = 'LU',
  MX = 'MX',
  MC = 'MC',
  MO = 'MO',
  MK = 'MK',
  MG = 'MG',
  MY = 'MY',
  MW = 'MW',
  ML = 'ML',
  MT = 'MT',
  MA = 'MA',
  MQ = 'MQ',
  MU = 'MU',
  MR = 'MR',
  YT = 'YT',
  FM = 'FM',
  MD = 'MD',
  MN = 'MN',
  ME = 'ME',
  MS = 'MS',
  MZ = 'MZ',
  NA = 'NA',
  NR = 'NR',
  NP = 'NP',
  NI = 'NI',
  NE = 'NE',
  NG = 'NG',
  NU = 'NU',
  NO = 'NO',
  NC = 'NC',
  NZ = 'NZ',
  OM = 'OM',
  NL = 'NL',
  PK = 'PK',
  PW = 'PW',
  PS = 'PS',
  PA = 'PA',
  PG = 'PG',
  PY = 'PY',
  PE = 'PE',
  PF = 'PF',
  PL = 'PL',
  PT = 'PT',
  PR = 'PR',
  QA = 'QA',
  GB = 'GB',
  CF = 'CF',
  CZ = 'CZ',
  DO = 'DO',
  SS = 'SS',
  RE = 'RE',
  RW = 'RW',
  RO = 'RO',
  RU = 'RU',
  EH = 'EH',
  WS = 'WS',
  AS = 'AS',
  BL = 'BL',
  KN = 'KN',
  SM = 'SM',
  MF = 'MF',
  PM = 'PM',
  VC = 'VC',
  SH = 'SH',
  LC = 'LC',
  ST = 'ST',
  SN = 'SN',
  RS = 'RS',
  SC = 'SC',
  SL = 'SL',
  SG = 'SG',
  SX = 'SX',
  SY = 'SY',
  SO = 'SO',
  LK = 'LK',
  ZA = 'ZA',
  SD = 'SD',
  SE = 'SE',
  CH = 'CH',
  SR = 'SR',
  SJ = 'SJ',
  SZ = 'SZ',
  TJ = 'TJ',
  TH = 'TH',
  TW = 'TW',
  TZ = 'TZ',
  IO = 'IO',
  TF = 'TF',
  TL = 'TL',
  TG = 'TG',
  TK = 'TK',
  TO = 'TO',
  TT = 'TT',
  TN = 'TN',
  TM = 'TM',
  TR = 'TR',
  TV = 'TV',
  UA = 'UA',
  UG = 'UG',
  UY = 'UY',
  UZ = 'UZ',
  VU = 'VU',
  VE = 'VE',
  VN = 'VN',
  WF = 'WF',
  YE = 'YE',
  DJ = 'DJ',
  ZM = 'ZM',
  ZW = 'ZW'
}
