export enum EAtmosphere {
  UNIVERSITARY = 'universitario',
  SELECT = 'selecto',
  YOUNG = 'young',
  INTERNATIONAL = 'internacional',
  ERASMUS = 'erasmus',
  URBAN = 'urban',
  UNDERGROUND = 'underground',
  ALTERNATIVE = 'alternativo',
  LGTBIQP = 'lgtbiqp',
  LGTBIQP2 = 'LGTBIQ+',
  LGTB_FRIENDLY = 'lgtb-friendly',
  PETARDEO = 'petardeo',
  BENE_FICIAL = 'benefico',
  THEA_TRICAL = 'teatral',
  HETERO_CURIOSO = 'hetero-curioso'
}
