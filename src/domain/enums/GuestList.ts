export enum EGuestListState {
  ACTIVATED = 'activada',
  ERROR = 'error',
  FILLED = 'rellena_cliente',
  QUEUED = 'en_cola',
  PROCESSING = 'procesando',
  REJECTED = 'rechazada'
}

export enum EGuestListSaleType {
  RRPP = 'rrpp',
  ONLINE = 'online',
  TAQUILLA = 'taquilla'
}

export enum EGuestListAcessState {
  CANCELED = 'anulada',
  PAID = 'pagado',
  PENDING = 'pendiente'
}

export enum EGuestListPaymentMethod {
  CASH = 'cash',
  CARD = 'card',
  ONLINE = 'online'
}

export enum EGuestListShipment {
  SIMPLE = 'simple',
  MULTIPLE = 'multiple',
  DATA = 'datos',
  PREDEFINED_DATA = 'datos_predefinidos',
  RECEPTION = 'recepcion'
}

export enum EGuestListRateTypes {
  LIMITED = 'limitada',
  OPEN = 'abierta',
  PUBLIC = 'publica'
}
