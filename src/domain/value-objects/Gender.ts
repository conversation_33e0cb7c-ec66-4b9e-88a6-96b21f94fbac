import { FvEnum } from './Enum';

type GenderDefault = {
  readonly code: EGender;
  readonly name: EGenderName;
  readonly group: EGenderGroup;
};

export enum EGender {
  FEMALE = '0',
  MALE = '1',
  OTHER = '2'
}

export enum EGenderName {
  FEMALE = 'chica',
  MALE = 'chico',
  OTHER = ''
}

export enum EGenderGroup {
  FEMALE = 'chica',
  MALE = 'chico',
  MIX = 'mixto',
  OTHER = ''
}

export class Gender extends FvEnum<EGender> {
  private constructor(value: EGender) {
    super(value, Gender.values());
  }

  static build(value: EGender): Gender {
    return new Gender(value);
  }

  static male(): Gender {
    return new Gender(EGender.MALE);
  }

  static female(): Gender {
    return new Gender(EGender.FEMALE);
  }

  static other(): Gender {
    return new Gender(EGender.OTHER);
  }

  static values(): EGender[] {
    return Object.values(EGender);
  }

  static names(): EGenderName[] {
    return Object.values(EGenderName);
  }

  static groups(): EGenderGroup[] {
    return Object.values(EGenderGroup);
  }

  static default(): GenderDefault {
    return {
      code: EGender.MALE,
      name: EGenderName.MALE,
      group: EGenderGroup.MALE,
    };
  }

  name(): EGenderName {
    const match: Record<EGender, EGenderName> = {
      [EGender.FEMALE]: EGenderName.FEMALE,
      [EGender.MALE]: EGenderName.MALE,
      [EGender.OTHER]: EGenderName.OTHER,
    };

    return match[this.value];
  }

  group(): EGenderGroup {
    const match: Record<EGender, EGenderGroup> = {
      [EGender.FEMALE]: EGenderGroup.FEMALE,
      [EGender.MALE]: EGenderGroup.MALE,
      [EGender.OTHER]: EGenderGroup.OTHER,
    };

    return match[this.value];
  }

  isFemale(): boolean {
    return this.equalTo(EGender.FEMALE);
  }

  isMale(): boolean {
    return this.equalTo(EGender.MALE);
  }

  isOther(): boolean {
    return this.equalTo(EGender.OTHER);
  }
}
