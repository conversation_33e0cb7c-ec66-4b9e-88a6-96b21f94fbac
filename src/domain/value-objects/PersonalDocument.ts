import { left, right } from '../contracts/Result';
import { InvalidArgumentError } from '../errors/InvalidArgumentError';

import { FvEnum } from './Enum';
import { CIF } from './PersonalDocument/CIF';
import { DNI } from './PersonalDocument/DNI';
import { NIE } from './PersonalDocument/NIE';
import { Passport } from './PersonalDocument/Passport';
import { ValueObject } from './ValueObject';

import type { Either } from '../contracts/Result';
import type { DocumentValidate } from './PersonalDocument/Contracts';

export enum EDocumentType {
  CIF = 'cif',
  DNI = 'dni',
  NIE = 'nie',
  OTHER = 'other',
  PASSPORT = 'passport'
}

export type PersonalDocumentPrimitives = {
  readonly number: string;
  readonly type: EDocumentType;
};

class PersonalDocumentType extends FvEnum<EDocumentType> {
  static readonly values = Object.values(EDocumentType);

  private constructor(value: EDocumentType) {
    super(value, PersonalDocumentType.values);
  }

  static build(value: EDocumentType): Either<InvalidArgumentError, PersonalDocumentType> {
    return this.values.includes(value)
      ? right(new PersonalDocumentType(value))
      : left(InvalidArgumentError.invalidDocumentType({
        context: this.constructor.name,
        target: value,
      }));
  }

  toPrimitive(): EDocumentType {
    return this.value;
  }
}

class PersonalDocumentNumber extends ValueObject<string> {
  static build(type: PersonalDocumentType, value: string): Either<InvalidArgumentError, PersonalDocumentNumber> {
    const matchValidation: Record<EDocumentType, () => DocumentValidate> = {
      [EDocumentType.CIF]: () => CIF.validate(value),
      [EDocumentType.DNI]: () => DNI.validate(value),
      [EDocumentType.NIE]: () => NIE.validate(value),
      [EDocumentType.PASSPORT]: () => Passport.validate(value),
      [EDocumentType.OTHER]: () => right(true),
    };

    const validation = matchValidation[type.value]();

    return validation.isLeft() ? left(validation.value) : right(new PersonalDocumentNumber(value));
  }

  toPrimitive(): string {
    return this.value;
  }
}

interface PersonalDocumentProps {
  readonly type: PersonalDocumentType;
  readonly number: PersonalDocumentNumber;
}

export class PersonalDocument extends ValueObject<PersonalDocumentProps> {
  private constructor(value: PersonalDocumentProps) {
    super(value);
  }

  static build(type: EDocumentType, number: string): Either<InvalidArgumentError, PersonalDocument> {
    const typeValueObjectResult = PersonalDocumentType.build(type);

    if (typeValueObjectResult.isLeft()) {
      return left(typeValueObjectResult.value);
    }

    const typeValueObject = typeValueObjectResult.value;
    const numberValueObjectResult = PersonalDocumentNumber.build(typeValueObject, number);

    if (numberValueObjectResult.isLeft()) {
      return left(numberValueObjectResult.value);
    }

    const valueObject = new PersonalDocument({
      type: typeValueObject,
      number: numberValueObjectResult.value,
    });

    return right(valueObject);
  }

  get type(): EDocumentType {
    return this.value.type.toPrimitive();
  }

  get number(): string {
    return this.value.number.toPrimitive();
  }

  toPrimitive(): PersonalDocumentPrimitives {
    return {
      type: this.type,
      number: this.number,
    };
  }
}
