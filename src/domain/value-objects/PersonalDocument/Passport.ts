import { left, right } from '@/domain/contracts/Result';
import { InvalidArgumentError } from '@/domain/errors/InvalidArgumentError';

import type { DocumentValidate, IPersonalDocumentValidate } from './Contracts';

export class Passport implements IPersonalDocumentValidate {
  private constructor(readonly number: string) {}

  private invalidArgumentError(): InvalidArgumentError {
    return InvalidArgumentError.invalidDocumentNumber({
      context: this.constructor.name,
      target: this.number,
    });
  }

  static validate(number: string): DocumentValidate {
    return new Passport(number).validate();
  }

  validate(): DocumentValidate {
    const matched = this.number.match(/^[A-Z0-9]{1,3}[A-Z0-9\s-]{4,14}$/);

    return !matched ? left(this.invalidArgumentError()) : right(true);
  }
}
