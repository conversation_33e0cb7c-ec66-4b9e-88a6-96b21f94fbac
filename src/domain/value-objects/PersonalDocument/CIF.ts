import { left, right } from '@/domain/contracts/Result';
import { InvalidArgumentError } from '@/domain/errors/InvalidArgumentError';

import type { DocumentValidate, IPersonalDocumentValidate } from './Contracts';

export class CIF implements IPersonalDocumentValidate {
  private constructor(readonly number: string) {}

  private invalidArgumentError(): InvalidArgumentError {
    return InvalidArgumentError.invalidDocumentNumber({
      context: this.constructor.name,
      target: this.number,
    });
  }

  static validate(number: string): DocumentValidate {
    return new CIF(number).validate();
  }

  private calculateControlDigit(number: string): number {
    const numberArray = number.split('');
    const numberArrayLength = numberArray.length;

    let controlDigit = 0;
    let even = false;

    for (let i = numberArrayLength - 1; i >= 0; i--) {
      let digit = Number(numberArray[i]);

      if (even) {
        digit *= 2;

        if (digit > 9) {
          digit = 1 + (digit % 10);
        }
      }

      controlDigit += digit;
      even = !even;
    }

    controlDigit %= 10;

    if (controlDigit !== 0) {
      controlDigit = 10 - controlDigit;
    }

    return controlDigit;
  }

  private checkControlls(letter: string, number: string, controlLetter: string): boolean {
    const letters = 'JABCDEFGHI';
    const controlLetters = 'PQSWKALE';

    const letterPosition = letters.indexOf(letter);
    const controlLetterPosition = controlLetters.indexOf(controlLetter);
    const controlDigit = this.calculateControlDigit(number);

    const controll1 = letterPosition >= 0 && controlLetterPosition === controlDigit;
    const controll2 = letterPosition < 0 && controlLetterPosition === controlDigit + 1;

    return controll1 || controll2;
  }

  validate(): DocumentValidate {
    const matched = this.number.match(/^([ABCDEFGHJKLMNPQRSUVW])(\d{7})([0-9A-J])$/);

    if (!matched) {
      return left(this.invalidArgumentError());
    }

    const [, letter, number, controlLetter] = matched;

    if (!letter || !number || !controlLetter) {
      return left(this.invalidArgumentError());
    }

    return this.checkControlls(letter, number, controlLetter) ? left(this.invalidArgumentError()) : right(true);
  }
}
