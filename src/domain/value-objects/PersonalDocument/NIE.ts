import { left } from '@/domain/contracts/Result';
import { InvalidArgumentError } from '@/domain/errors/InvalidArgumentError';

import { DNI } from './DNI';

import type { DocumentValidate, IPersonalDocumentValidate } from './Contracts';

export class NIE implements IPersonalDocumentValidate {
  private readonly prefixs: Map<string, number>;

  private constructor(readonly number: string) {
    this.prefixs = new Map<string, number>().set('X', 0).set('Y', 1).set('Z', 2);
  }

  private invalidArgumentError(): InvalidArgumentError {
    return InvalidArgumentError.invalidDocumentNumber({
      context: this.constructor.name,
      target: this.number,
    });
  }

  static validate(number: string): DocumentValidate {
    return new NIE(number).validate();
  }

  validate(): DocumentValidate {
    const matched = this.number.match(/^[XYZ]\d{7,8}[A-Z]$/);

    if (!matched) {
      return left(this.invalidArgumentError());
    }

    const prefix = this.number.charAt(0);

    if (!this.prefixs.has(prefix)) {
      return left(this.invalidArgumentError());
    }

    const dniNumber = `${this.prefixs.get(prefix)}${this.number.substring(1)}`;

    return DNI.validate(dniNumber);
  }
}
