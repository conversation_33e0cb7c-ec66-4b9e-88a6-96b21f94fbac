import { left, right } from '@/domain/contracts/Result';
import { InvalidArgumentError } from '@/domain/errors/InvalidArgumentError';

import type { DocumentValidate, IPersonalDocumentValidate } from './Contracts';

export class DNI implements IPersonalDocumentValidate {
  private constructor(readonly number: string) {}

  private invalidArgumentError(): InvalidArgumentError {
    return InvalidArgumentError.invalidDocumentNumber({
      context: this.constructor.name,
      target: this.number,
    });
  }


  static validate(number: string): DocumentValidate {
    return new DNI(number).validate();
  }

  validate(): DocumentValidate {
    const matched = this.number.match(/^(\d{8})([A-Z])$/);

    if (!matched) {
      return left(this.invalidArgumentError());
    }

    const [, number, letter] = matched;

    if (!number || !letter) {
      return left(this.invalidArgumentError());
    }

    const letters = 'TRWAGMYFPDXBNJZSQVHLCKET';
    const letterPosition = +number % (letters.length - 1);

    if (letter !== letters[letterPosition]) {
      return left(this.invalidArgumentError());
    }

    return right(true);
  }
}
