import { FvDate } from './Date';
import { DateStamp } from './DateStamp';
import { ValueObject } from './ValueObject';

import type { DatePrimitive } from '../contracts/Primitives';
import type { DateTypes } from './DateStamp';

export class UpdatedAt extends ValueObject<Date> {
  static build(value: DateTypes): UpdatedAt {
    const stampValue = DateStamp.build(value);

    return new UpdatedAt(stampValue);
  }

  static create(): UpdatedAt {
    const date = FvDate.create().toPrimitive();

    return new UpdatedAt(date);
  }

  toPrimitive(): DatePrimitive {
    return this.value;
  }

  toMilliseconds(): number {
    return FvDate.create(this.value).toMilliseconds();
  }

  toISO(): string {
    return FvDate.create(this.value).toISO();
  }
}
