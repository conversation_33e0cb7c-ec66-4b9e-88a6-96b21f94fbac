import parsePhoneNumberFromString from 'libphonenumber-js';

import { left, right } from '../contracts/Result';
import { ECountryCode } from '../enums/CountryCode';
import { InvalidArgumentError } from '../errors/InvalidArgumentError';
import { UnexpectedError } from '../errors/UnexpectedError';

import { ValueObject } from './ValueObject';

import type { CountryCode } from 'libphonenumber-js';
import type { Either } from '../contracts/Result';

type PhoneNumberProps = {
  readonly number: string;
  readonly formatted: string;
  readonly country: ECountryCode;
};

export type PhoneNumberPrimitives = {
  readonly number: string;
  readonly country: ECountryCode;
};

type Options = {
  readonly country: ECountryCode;
};

export class PhoneNumber extends ValueObject<PhoneNumberProps> {
  private constructor(value: PhoneNumberProps) {
    super(value);
  }

  static build(value: string, options?: Options): Either<UnexpectedError | InvalidArgumentError, PhoneNumber> {
    const validation = PhoneNumber.validate(value, options);

    if (validation.isLeft()) {
      return left(validation.value);
    }

    return right(new PhoneNumber(validation.value));
  }

  private static validate(value: string, options?: Options): Either<UnexpectedError | InvalidArgumentError, PhoneNumberProps> {
    try {
      const countryCode = options?.country ?? ECountryCode.ES;
      const response = parsePhoneNumberFromString(value, countryCode as CountryCode);

      if (!response?.isValid()) {
        return left(InvalidArgumentError.invalidPhone({
          context: this.constructor.name,
          target: value,
        }));
      }

      return right({
        number: response.number,
        formatted: response.formatInternational(),
        country: (response.country ?? countryCode) as ECountryCode,
      });
    } catch (error) {
      const castedError = error as Error;

      return left(UnexpectedError.build({
        context: 'PhoneNumber',
        error: castedError,
      }));
    }
  }

  get number(): string {
    return this.value.number;
  }

  get country(): ECountryCode {
    return this.value.country;
  }

  toPrimitive(): PhoneNumberPrimitives {
    return {
      number: this.number,
      country: this.country,
    };
  }
}
