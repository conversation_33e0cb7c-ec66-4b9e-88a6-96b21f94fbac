/* eslint-disable @typescript-eslint/no-explicit-any */

export type ValueObjectProps = string | number | boolean | Date | Record<string, any>;

export interface Comparable<T> {
  isEqualTo(value: T): boolean;
  isLessThan(value: T): boolean;
  isGreater<PERSON>han(value: T): boolean;
  compareTo(value: T): number;
}

export abstract class ValueObject<T extends ValueObjectProps> {
  protected constructor(readonly value: T) {}

  equals(other: ValueObject<T> | string): boolean {
    if (typeof other === 'string') {
      return this.value === other;
    }

    if (this.constructor.name !== other.constructor.name) {
      return false;
    }

    if (typeof other.value === 'object') {
      return JSON.stringify(this.value) === JSON.stringify(other.value);
    }

    return this.value === other.value;
  }

  toString(): string {
    return String(this.value);
  }
}
