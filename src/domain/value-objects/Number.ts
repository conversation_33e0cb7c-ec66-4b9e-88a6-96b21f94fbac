import BigNumber from 'bignumber.js';

import { DEFAULT_LENGUAGE } from '../config/Config';

import { ValueObject } from './ValueObject';

import type { Comparable } from './ValueObject';

export class FvNumber extends ValueObject<number> implements Comparable<number> {
  private getValue(other: FvNumber | number): number {
    return FvNumber.is(other) ? other : other.toPrimitive();
  }

  static build(value: number): FvNumber {
    return new this(value);
  }

  static createZero(): FvNumber {
    return new this(0);
  }

  static random(): FvNumber {
    const newValue = Math.random();

    return new this(newValue);
  }

  static max(...numbers: number[]): FvNumber {
    const maxValue = BigNumber.max(...numbers).toNumber();

    return new FvNumber(maxValue);
  }

  static min(...numbers: number[]): FvNumber {
    const minValue = BigNumber.min(...numbers).toNumber();

    return new FvNumber(minValue);
  }

  static isEven(number: number): boolean {
    return new BigNumber(number).mod(2).isZero();
  }

  static isOdd(number: number): boolean {
    return !this.isEven(number);
  }

  static is(value: unknown): value is number {
    return typeof value === 'number' && !Number.isNaN(value);
  }

  static isFinite(value: unknown): boolean {
    return Number.isFinite(value);
  }

  static avg(...numbers: (FvNumber | number)[]): FvNumber {
    if (numbers.length === 0) {
      return FvNumber.createZero();
    }

    const numberResult = numbers.reduce((acc, item) => {
      const number = FvNumber.is(item) ? item : item.toPrimitive();

      return acc.plus(new BigNumber(number));
    }, new BigNumber(0));

    const avg = numberResult.div(numbers.length);

    return new FvNumber(avg.toNumber());
  }

  toPercentage(): number {
    return new BigNumber(this.value).div(100).toNumber();
  }

  floor(): FvNumber {
    const newValue = new BigNumber(this.value).integerValue(BigNumber.ROUND_FLOOR);

    return new FvNumber(newValue.toNumber());
  }

  ceil(): FvNumber {
    const newValue = new BigNumber(this.value).integerValue(BigNumber.ROUND_CEIL);

    return new FvNumber(newValue.toNumber());
  }

  trunc(): FvNumber {
    const newValue = new BigNumber(this.value).integerValue(BigNumber.ROUND_DOWN);

    return new FvNumber(newValue.toNumber());
  }

  round(): FvNumber {
    const newValue = new BigNumber(this.value).integerValue(BigNumber.ROUND_HALF_UP);

    return new FvNumber(newValue.toNumber());
  }

  abs(): FvNumber {
    const newValue = new BigNumber(this.value).abs();

    return new FvNumber(newValue.toNumber());
  }

  calculatePercentage(percentage: FvNumber | number): FvNumber {
    const percentageValue = this.getValue(percentage);
    const newValue = new BigNumber(this.value).times(percentageValue).div(100);

    return new FvNumber(newValue.toNumber());
  }

  add(other: FvNumber | number): FvNumber {
    const newValue = new BigNumber(this.value).plus(this.getValue(other));

    return new FvNumber(newValue.toNumber());
  }

  subtract(other: FvNumber | number): FvNumber {
    const newValue = new BigNumber(this.value).minus(this.getValue(other));

    return new FvNumber(newValue.toNumber());
  }

  multiply(other: FvNumber | number): FvNumber {
    const newValue = new BigNumber(this.value).times(this.getValue(other));

    return new FvNumber(newValue.toNumber());
  }

  divide(other: FvNumber | number): FvNumber {
    const newValue = new BigNumber(this.value).div(this.getValue(other));

    return new FvNumber(newValue.toNumber());
  }

  toPrimitive(): number {
    return this.value;
  }

  isEven(): boolean {
    return FvNumber.isEven(this.toPrimitive());
  }

  isOdd(): boolean {
    return FvNumber.isOdd(this.toPrimitive());
  }

  isGreaterThan(other: FvNumber | number): boolean {
    return new BigNumber(this.value).isGreaterThan(this.getValue(other));
  }

  isGreaterThanOrEqualTo(other: FvNumber | number): boolean {
    return new BigNumber(this.value).isGreaterThanOrEqualTo(this.getValue(other));
  }

  isLessThan(other: FvNumber | number): boolean {
    return new BigNumber(this.value).isLessThan(this.getValue(other));
  }

  isLessThanOrEqualTo(other: FvNumber | number): boolean {
    return new BigNumber(this.value).isLessThanOrEqualTo(this.getValue(other));
  }

  isEqualTo(other: FvNumber | number): boolean {
    return new BigNumber(this.value).isEqualTo(this.getValue(other));
  }

  compareTo(value: FvNumber | number): number {
    return this.subtract(value).toPrimitive();
  }

  isPercentageValid(): boolean {
    return new BigNumber(this.value).gte(0) && new BigNumber(this.value).lte(100);
  }

  isZero(): boolean {
    return new BigNumber(this.value).isZero();
  }

  isPositive(): boolean {
    return new BigNumber(this.value).isGreaterThan(0);
  }

  isNegative(): boolean {
    return new BigNumber(this.value).isLessThan(0);
  }

  convertPercentage(): FvNumber {
    return this.multiply(100);
  }

  format(locale: string = DEFAULT_LENGUAGE): string {
    return new Intl.NumberFormat(locale).format(this.toPrimitive());
  }

  mod(other: FvNumber | number): FvNumber {
    const newValue = new BigNumber(this.value).mod(this.getValue(other));

    return new FvNumber(newValue.toNumber());
  }

  isMultipleOf(other: FvNumber | number): boolean {
    return this.mod(other).isZero();
  }
}
