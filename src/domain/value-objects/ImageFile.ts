import { Maybe } from '../contracts/Maybe';
import { left, right } from '../contracts/Result';
import { ImageError } from '../errors/ImageError';

import { ValueObject } from './ValueObject';

import type { Either } from '../contracts/Result';

type ImageFileProps = {
  readonly name: string;
  readonly extension: string;
  readonly mimetype: string;
  readonly content: Buffer;
};

export class ImageFile extends ValueObject<ImageFileProps> {
  private constructor(
    private readonly props: ImageFileProps,
  ) {
    super(props);
  }

  private static readonly extensions = new Set([
    'apng',
    'avif',
    'jfif',
    'jpg',
    'jpeg',
    'pjp',
    'pjpeg',
    'png',
    'webp',
  ]);

  static build(name: string, mimetype: string, content: Buffer): Either<ImageError, ImageFile> {
    const _extension = this.getExtension(name);

    if (_extension.isEmpty()) {
      return left(ImageError.invalidFormat({ context: 'ImageFile', data: { value: name, mimetype } }));
    }

    const extension = _extension.get();

    const isValidOrError = this.ensureExtension(extension);

    if (isValidOrError.isLeft()) {
      return left(isValidOrError.value);
    }

    return right(new ImageFile({
      name, extension, mimetype, content,
    }));
  }

  private static getExtension(name: string): Maybe<string> {
    const fragments = name.split('.');
    const extension = fragments.at(-1);

    return Maybe.fromValue(extension);
  }

  get name(): string {
    return this.props.name;
  }

  get extension(): string {
    return this.props.extension;
  }

  get mimetype(): string {
    return this.props.mimetype;
  }

  get content(): Buffer {
    return this.props.content;
  }

  private static ensureExtension(extension: string): Either<ImageError, true> {
    if (!this.extensions.has(extension)) {
      return left(ImageError.invalidExtension({ context: 'ImageFile', data: { value: extension } }));
    }

    return right(true);
  }
}
