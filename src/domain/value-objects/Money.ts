import * as currencies from '@dinero.js/currencies';
import BigNumber from 'bignumber.js';
import {
  add,
  dinero,
  equal,
  greaterThan,
  greaterThanOrEqual,
  isNegative,
  isPositive,
  isZero,
  lessThan,
  lessThanOrEqual,
  maximum,
  minimum,
  subtract,
  toDecimal,
} from 'dinero.js';

import { DEFAULT_LENGUAGE } from '@/domain/config/Config';

import { left, right } from '../contracts/Result';
import { MoneyError } from '../errors/MoneyError';

import { FvNumber } from './Number';
import { ValueObject } from './ValueObject';

import type { Dinero } from 'dinero.js';
import type { Either } from '../contracts/Result';
import type { ECurrency } from '../enums/Currencies';

type Amount = number;

export type MoneyProps = {
  readonly amount: Amount;
  readonly currency: ECurrency;
};

export type MoneyEither = Either<MoneyError, Money>;

export class Money extends ValueObject<MoneyProps> {
  private readonly defaultLenguage = DEFAULT_LENGUAGE;

  private static readonly ROUND_STRATEGY = BigNumber.ROUND_HALF_EVEN; // redondeo bancario (dinero.js default)

  protected constructor(private readonly money: Dinero<Amount>) {
    const { amount, currency } = money.toJSON();

    super({
      amount,
      currency: currency.code as ECurrency,
    });
  }

  private static getBase(base: number | readonly number[]): number {
    if (FvNumber.is(base)) {
      return base;
    }

    return base.slice().shift() as number;
  }

  private static roundToDecimals(amount: number, decimals: number): number {
    const parsedAmount = new BigNumber(amount).decimalPlaces(decimals, Money.ROUND_STRATEGY);

    return parsedAmount.toNumber();
  }

  private static makeAmount(amount: number, parsedCurrency: currencies.Currency<Amount>): Amount {
    const roundAmount = this.roundToDecimals(amount, parsedCurrency.exponent);

    const base = this.getBase(parsedCurrency.base);
    const parsedAmount = new BigNumber(roundAmount);
    const parsedBase = new BigNumber(base);

    const parsedExponent = parsedBase.pow(parsedCurrency.exponent);
    const newAmount = parsedAmount.multipliedBy(parsedExponent);

    return newAmount.toNumber();
  }

  private static buildCore(amount: number, currency: ECurrency): Dinero<Amount> {
    const parsedCurrency = currencies[currency];
    const parsedAmount = this.makeAmount(amount, parsedCurrency);

    return dinero({
      amount: parsedAmount,
      currency: parsedCurrency,
    });
  }

  private numberFormat(locale: string, currency: ECurrency, format: number): string {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
    }).format(format);
  }

  static build(props: MoneyProps): MoneyEither {
    const { amount, currency } = props;
    const parsedCurrency = currencies[currency];

    if (!parsedCurrency) {
      return left(MoneyError.currencyNotSupported({
        context: this.constructor.name,
        data: {
          amount,
          currency,
        },
      }));
    }

    try {
      const instance = this.buildCore(amount, currency);

      return right(new Money(instance));
    } catch (error) {
      const castedError = error as Error;

      return left(MoneyError.build({
        context: 'Money',
        error: castedError,
        data: {
          amount,
          currency,
        },
      }));
    }
  }

  static buildZero(currency: ECurrency): Money {
    const amount = 0;

    return new Money(this.buildCore(amount, currency));
  }

  static getMinimum(money: Money[]): Money {
    const newValue = minimum(money.map(item => item.money));

    return new Money(newValue);
  }

  static getMaximum(money: Money[]): Money {
    const newValue = maximum(money.map(item => item.money));

    return new Money(newValue);
  }

  format(locale?: string): string {
    const _locale = locale || this.defaultLenguage;

    try {
      return this.numberFormat(_locale, this.value.currency, this.toDecimal());
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
      return this.numberFormat(this.defaultLenguage, this.value.currency, this.toDecimal());
    }
  }

  add(money: Money | number): Money {
    if (FvNumber.is(money)) {
      const moneyOrError = Money.build({
        amount: money,
        currency: this.currency,
      });

      if (moneyOrError.isLeft()) {
        throw moneyOrError.value.contextualize({
          context: this.constructor.name,
          data: {
            amount: money,
            currency: this.currency,
          },
          error: moneyOrError.value,
        });
      }

      money = moneyOrError.value;
    }

    try {
      const newValue = add(this.money, money.money);

      return new Money(newValue);
    } catch (error) {
      const parsedError = error as Error;

      throw MoneyError.incompatibleCurrencies({
        context: this.constructor.name,
        error: parsedError,
        data: {
          amount1: this.amount,
          currency1: this.currency,
          amount2: money.amount,
          currency2: money.currency,
        },
      });
    }
  }

  subtract(money: Money | number): Money {
    if (FvNumber.is(money)) {
      const moneyOrError = Money.build({
        amount: money,
        currency: this.currency,
      });

      if (moneyOrError.isLeft()) {
        throw new Error(moneyOrError.value.toString());
      }

      money = moneyOrError.value;
    }

    const newValue = subtract(this.money, money.money);

    return new Money(newValue);
  }

  multiply(factor: number): Money {
    const parsedAmount = new BigNumber(toDecimal(this.money));

    const partialResult = parsedAmount.multipliedBy(factor);

    const newValue = Money.buildCore(partialResult.toNumber(), this.currency);

    return new Money(newValue);
  }

  percentage(percentage: number): Money {
    const parsedPercentage = new BigNumber(percentage).dividedBy(100);
    const parsedAmount = new BigNumber(toDecimal(this.money));

    const partialResult = parsedAmount.multipliedBy(parsedPercentage);

    const newValue = Money.buildCore(partialResult.toNumber(), this.currency);

    return new Money(newValue);
  }

  /**
   * Used to add a percentage to an existing amount.
   *
   * @example
   * const money = Money.build({amount: 100, currency: 'EUR'}).addPercentage(15); // --> 100 * 1.15 = 115
   */
  addPercentage(percentage: number): Money {
    const parsedPercentage = new BigNumber(percentage).dividedBy(100);
    const parsedAmount = new BigNumber(toDecimal(this.money));

    const partialResult = parsedAmount.multipliedBy(parsedPercentage);
    const totalResult = partialResult.plus(this.amount);

    const newValue = Money.buildCore(totalResult.toNumber(), this.currency);

    return new Money(newValue);
  }

  /**
   * Used to subtract a percentage to an existing amount.
   *
   * @example
   * const money = Money.build({amount: 100, currency: 'EUR'}).subtractPercentage(15); // --> 100 * 0.85 = 85
   */
  subtractPercentage(percentage: number): Money {
    const parsedPercentage = new BigNumber(percentage).dividedBy(100);
    const parsedAmount = new BigNumber(toDecimal(this.money));

    const partialResult = parsedAmount.multipliedBy(parsedPercentage);
    const totalResult = parsedAmount.minus(partialResult);

    const newValue = Money.buildCore(totalResult.toNumber(), this.currency);

    return new Money(newValue);
  }

  divided(quantity: number): Money {
    const parsedAmount = new BigNumber(toDecimal(this.money));

    const partialResult = parsedAmount.dividedBy(quantity);

    const newValue = Money.buildCore(partialResult.toNumber(), this.currency);

    return new Money(newValue);
  }

  /* toJSON(): DineroSnapshot<Amount> {
    return this.money.toJSON();
  } */

  /**
   * Used to get isolated amount without currency.
   */
  toDecimal(): number {
    return Number(toDecimal(this.money));
  }

  /**
   * Used to cast the .toDecimal to string
   */
  toString(): string {
    return toDecimal(this.money);
  }

  /*   toUnits(): readonly Amount[] {
    return toUnits(this.money);
  } */

  isPositive(): boolean {
    return isPositive(this.money);
  }

  isNegative(): boolean {
    return isNegative(this.money);
  }

  isZero(): boolean {
    return isZero(this.money);
  }

  gte(money: Money): boolean {
    return greaterThanOrEqual(this.money, money.money);
  }

  gt(money: Money): boolean {
    return greaterThan(this.money, money.money);
  }

  lte(money: Money): boolean {
    return lessThanOrEqual(this.money, money.money);
  }

  lt(money: Money): boolean {
    return lessThan(this.money, money.money);
  }

  eq(money: Money): boolean {
    return equal(this.money, money.money);
  }

  /**
   * Used to get isolated currency without amount.
   */
  get currency(): ECurrency {
    return this.value.currency;
  }

  /**
   * Used to get isolated amount without currency.
   * Use .toDecimal instead of this one.
   */
  get amount(): Amount {
    return this.toDecimal();
  }

  /**
   * Used to get amount and currency together.
   */
  toPrimitive(): MoneyProps {
    return {
      amount: this.amount,
      currency: this.currency,
    };
  }

  toMinorUnits(): number {
    return this.money.toJSON().amount;
  }
}
