import { RandomIdGenerator } from '../services/RandomIdGenerator';

import { Identifier } from './Identifier';

export class UniqueEntityID extends Identifier<string> {
  static pattern = '^[0-9a-zA-Z]{32}$';

  static create(): UniqueEntityID {
    const newValue = RandomIdGenerator.create();

    return this.build(newValue);
  }

  static createObjectId(): UniqueEntityID {
    const newValue = RandomIdGenerator.createObjectId();

    return this.build(newValue);
  }

  static build(value: string): UniqueEntityID {
    return new this(value);
  }

  static isValid(value: string): boolean {
    return RandomIdGenerator.isValid(value);
  }
}
