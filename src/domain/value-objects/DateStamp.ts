import { Maybe } from '../contracts/Maybe';

import { FvDate } from './Date';
import { FvNumber } from './Number';
import { FvString } from './String';

export type DateTypes = number | Date | string;

export class DateStamp {
  static build(value: DateTypes): Date {
    if (FvNumber.is(value)) {
      return FvDate.createFromMilliSeconds(value).toPrimitive();
    }

    if (FvString.is(value)) {
      const dateOrError = FvDate.createFromISO(value);

      const date = dateOrError.isLeft()
        ? FvDate.create()
        : dateOrError.value;

      return date.toPrimitive();
    }

    return value;
  }

  static buildMaybe(value: DateTypes | null): Maybe<Date> {
    if (FvNumber.is(value)) {
      if (value === 0) {
        return Maybe.none();
      }

      const date = FvDate.createFromMilliSeconds(value).toPrimitive();

      return Maybe.some(date);
    }

    if (FvString.is(value)) {
      const dateOrError = FvDate.createFromISO(value);

      const date = dateOrError.isLeft()
        ? FvDate.create()
        : dateOrError.value;

      return Maybe.some(date.toPrimitive());
    }

    return Maybe.fromValue(value);
  }
}
