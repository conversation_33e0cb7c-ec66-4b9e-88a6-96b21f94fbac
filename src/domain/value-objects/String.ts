import CryptoJS from 'crypto-js';

import { FvNumber } from './Number';
import { ValueObject } from './ValueObject';

import type { Comparable } from './ValueObject';

export class FvString extends ValueObject<string> implements Comparable<string> {
  private getValue(other: FvString | string): string {
    return FvString.is(other) ? other : other.toPrimitive();
  }

  static build(value: string): FvString {
    return new this(value);
  }

  static empty(): FvString {
    return new this('');
  }

  static is(value: unknown): value is string {
    return typeof value === 'string';
  }

  /**
   * @example
   * const string = FvString.empty(""); // true
   * const string = FvString.empty("hi"); // false
   */
  isEmpty(): boolean {
    return this.value.length === 0;
  }

  toPrimitive(): string {
    return this.value;
  }

  isEqualTo(value: FvString | string): boolean {
    const other = this.getValue(value);

    return this.toPrimitive().localeCompare(other) === 0;
  }

  isLessThan(value: FvString | string): boolean {
    const other = this.getValue(value);

    return this.toPrimitive().localeCompare(other) === -1;
  }

  isGreaterThan(value: FvString | string): boolean {
    const other = this.getValue(value);

    return this.toPrimitive().localeCompare(other) === 1;
  }

  compareTo(value: FvString | string): number {
    const other = this.getValue(value);

    return this.toPrimitive().localeCompare(other);
  }

  removeSpaces(): FvString {
    return FvString.removeSpaces(this.value);
  }

  normalize(): FvString {
    return FvString.normalize(this.value);
  }

  diacriticInsensitive(): FvString {
    return FvString.diacriticInsensitive(this.value);
  }

  slugify(): FvString {
    const newValue = this.normalize().toPrimitive().toLowerCase()
      .replace(/ +/g, '-')
      .replace(/[^a-z0-9-_]/g, '')
      .trim();

    return FvString.build(newValue);
  }

  replace(searchValue: string | RegExp, replaceValue: string): FvString {
    const newValue = this.toPrimitive().replace(searchValue, replaceValue);

    return FvString.build(newValue);
  }

  toLowerCase(): FvString {
    const newValue = this.toPrimitive().toLowerCase();

    return FvString.build(newValue);
  }

  toUpperCase(): FvString {
    const newValue = this.toPrimitive().toUpperCase();

    return FvString.build(newValue);
  }

  toCamelCase(): FvString {
    const currentValue = this.toPrimitive();

    const newValue = currentValue
      .charAt(0)
      .toLowerCase()
      .concat(currentValue.slice(1));

    return FvString.build(newValue);
  }

  concat(value: FvString | string): FvString {
    const other = this.getValue(value);
    const newValue = this.toPrimitive().concat(other);

    return FvString.build(newValue);
  }

  static random(length: number): FvString {
    const size = FvNumber
      .build(length)
      .divide(2)
      .ceil()
      .toPrimitive();

    const newRandomString = CryptoJS.lib.WordArray
      .random(size)
      .toString(CryptoJS.enc.Hex)
      .slice(0, length);

    return FvString.build(newRandomString);
  }

  static removeSpaces(value: unknown): FvString {
    if (!FvString.is(value)) {
      return FvString.build('');
    }

    const newValue = value.replace(/[\s\t]+/g, '');

    return FvString.build(newValue);
  }

  static normalize(value: string): FvString {
    const match = new Map<string, string[]>()
      .set('A', ['Ã', 'À', 'Á', 'Ä', 'Â'])
      .set('E', ['È', 'É', 'Ë', 'Ê'])
      .set('I', ['Ì', 'Í', 'Ï', 'Î'])
      .set('O', ['Ò', 'Ó', 'Ö', 'Ô'])
      .set('U', ['Ù', 'Ú', 'Ü', 'Û'])
      .set('a', ['ã', 'à', 'á', 'ä', 'â'])
      .set('e', ['è', 'é', 'ë', 'ê'])
      .set('i', ['ì', 'í', 'ï', 'î'])
      .set('o', ['ò', 'ó', 'ö', 'ô'])
      .set('u', ['ù', 'ú', 'ü', 'û'])
      .set('n', ['Ñ', 'ñ', 'ň'])
      .set('c', ['Ç', 'ç']);

    match.forEach((badValues: string[], goodValue: string) => {
      badValues.forEach((badValue: string) => {
        value = value.replaceAll(badValue, goodValue);
      });
    });

    return new this(value);
  }

  static diacriticInsensitive(value: unknown): FvString {
    if (!FvString.is(value)) {
      return FvString.build('');
    }

    const newValue = value
      .replace(/a/g, '[a,á,à,ä]')
      .replace(/e/g, '[e,é,ë]')
      .replace(/i/g, '[i,í,ï]')
      .replace(/o/g, '[o,ó,ö,ò]')
      .replace(/u/g, '[u,ü,ú,ù]');

    return FvString.build(newValue);
  }
}
