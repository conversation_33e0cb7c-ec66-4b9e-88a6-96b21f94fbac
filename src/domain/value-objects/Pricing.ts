import { left, right } from '@/domain/contracts/Result';
import { InvalidArgumentError } from '@/domain/errors/InvalidArgumentError';
import { FvEnum } from '@/domain/value-objects/Enum';
import { Money } from '@/domain/value-objects/Money';
import { FvNumber } from '@/domain/value-objects/Number';
import { ValueObject } from '@/domain/value-objects/ValueObject';

import { ECustomFeeTypeCalculation } from '../enums/CustomFeeType';

import type { Either } from '@/domain/contracts/Result';
import type { ECurrency } from '@/domain/enums/Currencies';
import type { MoneyProps } from '@/domain/value-objects/Money';

type PricingSummary = {
  base: Money;
  calculated: Money;
  total: Money;
};

interface PricingAmount extends PricingAmountCalculator {
  toPrimitive(): MoneyProps | number;
}

interface PricingAmountCalculator {
  calculate(baseAmount: Money): PricingSummary;
}

interface PricingTypeValidator {
  isPercentage(): boolean;
  isFixed(): boolean;
}

class PricingType extends FvEnum<ECustomFeeTypeCalculation> implements PricingTypeValidator {
  static readonly values = Object.values(ECustomFeeTypeCalculation);

  private constructor(value: ECustomFeeTypeCalculation) {
    super(value, PricingType.values);
  }

  static build(value: ECustomFeeTypeCalculation): Either<InvalidArgumentError, PricingType> {
    return this.values.includes(value)
      ? right(new PricingType(value))
      : left(InvalidArgumentError.buildInvalidPricingType({
        context: this.constructor.name,
        target: value,
      }));
  }

  toPrimitive(): ECustomFeeTypeCalculation {
    return this.value;
  }

  isPercentage(): boolean {
    return this.value === ECustomFeeTypeCalculation.PERCENTAGE;
  }

  isFixed(): boolean {
    return this.value === ECustomFeeTypeCalculation.FIXED;
  }
}

class PercentagePricing extends ValueObject<FvNumber> implements PricingAmount {
  private constructor(
    private readonly amount: FvNumber,
  ) {
    super(amount);
  }

  static build(props: number): Either<InvalidArgumentError, PercentagePricing> {
    return right(new PercentagePricing(FvNumber.build(props)));
  }

  toPrimitive(): number {
    return this.amount.toPrimitive();
  }

  /**
   * Used to calculate the pricing summary for a percentage pricing.
   *
   * @example
   * const percentagePricing = PercentagePricing.build(15);
   * const baseAmount = Money.build({amount: 100, currency: 'EUR'});
   * const pricingSummary = percentagePricing.calculate(baseAmount);
   * // pricingSummary.base === 100
   * // pricingSummary.calculated === 15
   * // pricingSummary.total === 115
   */
  calculate(baseAmount: Money): PricingSummary {
    const percentage = baseAmount.percentage(this.toPrimitive());

    return {
      base: baseAmount,
      calculated: percentage,
      total: baseAmount.add(percentage),
    };
  }
}

class FixedPricing extends ValueObject<Money> implements PricingAmount {
  private constructor(private readonly amount: Money) {
    super(amount);
  }

  static build(props: MoneyProps): Either<InvalidArgumentError, FixedPricing> {
    const { amount, currency } = props;

    const moneyOrError = Money.build({ amount, currency });

    if (moneyOrError.isLeft()) {
      return left(moneyOrError.value);
    }

    return right(new FixedPricing(moneyOrError.value));
  }

  getAmount(): number {
    return this.amount.toDecimal();
  }

  getCurrency(): ECurrency {
    return this.amount.currency;
  }

  toPrimitive(): MoneyProps {
    return {
      amount: this.getAmount(),
      currency: this.getCurrency(),
    };
  }

  /**
   * Used to calculate the pricing summary for a fixed pricing.
   *
   * @example
   * const fixedPricing = FixedPricing.build({amount: 100, currency: 'EUR'});
   * const baseAmount = Money.build({amount: 100, currency: 'EUR'});
   * const pricingSummary = fixedPricing.calculate(baseAmount);
   * // pricingSummary.base === 100
   * // pricingSummary.calculated === 100
   * // pricingSummary.total === 200
   */
  calculate(baseAmount: Money): PricingSummary {
    return {
      base: baseAmount,
      calculated: this.value,
      total: baseAmount.add(this.value),
    };
  }
}

export type PricingProps = {
  readonly type: PricingType;
  readonly amount: PricingAmount;
};

export type PricingPrimitives = {
  readonly type: ECustomFeeTypeCalculation;
  readonly amount: MoneyProps | number;
};

export class Pricing extends ValueObject<PricingProps> implements PricingAmountCalculator, PricingTypeValidator {
  private constructor(
    private readonly type: PricingType,
    private readonly amount: PricingAmount,
  ) {
    super({ type, amount });
  }

  static build(type: ECustomFeeTypeCalculation, amount: MoneyProps): Either<InvalidArgumentError, Pricing> {
    const typeOrError = PricingType.build(type);

    if (typeOrError.isLeft()) {
      return left(typeOrError.value);
    }

    const typeVO = typeOrError.value;

    if (typeVO.isPercentage()) {
      const amountOrError = PercentagePricing.build(amount.amount);

      if (amountOrError.isLeft()) {
        return left(amountOrError.value);
      }

      return right(new Pricing(typeVO, amountOrError.value));
    }

    const amountOrError = FixedPricing.build(amount);

    if (amountOrError.isLeft()) {
      return left(amountOrError.value);
    }

    return right(new Pricing(typeVO, amountOrError.value));
  }

  isPercentage(): boolean {
    return this.type.isPercentage();
  }

  isFixed(): boolean {
    return this.type.isFixed();
  }

  calculate(baseAmount: Money): PricingSummary {
    return this.amount.calculate(baseAmount);
  }

  toPrimitive(): PricingPrimitives {
    return {
      type: this.type.toPrimitive(),
      amount: this.amount.toPrimitive(),
    };
  }
}
