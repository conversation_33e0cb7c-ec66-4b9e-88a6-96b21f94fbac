import { Maybe } from '../contracts/Maybe';

import { FvDate } from './Date';
import { DateStamp } from './DateStamp';
import { ValueObject } from './ValueObject';

import type { DatePrimitive } from '../contracts/Primitives';
import type { DateTypes } from './DateStamp';

export class RemovedAt extends ValueObject<Maybe<Date>> {
  static build(value: DateTypes | null): RemovedAt {
    const stampValue = DateStamp.buildMaybe(value);

    return new RemovedAt(stampValue);
  }

  static create(): RemovedAt {
    const date = FvDate.create().toPrimitive();

    return new RemovedAt(Maybe.some(date));
  }

  toPrimitive(): Maybe<DatePrimitive> {
    return this.value;
  }

  toMilliseconds(): number {
    if (this.value.isEmpty()) {
      return 0;
    }

    return FvDate.create(this.value.get()).toMilliseconds();
  }

  toISO(): Maybe<string> {
    if (this.value.isEmpty()) {
      return Maybe.none();
    }

    return Maybe.some(FvDate.create(this.value.get()).toISO());
  }

  isActive(): boolean {
    return this.value.isEmpty();
  }

  isRemoved(): boolean {
    return this.value.isDefined();
  }
}
