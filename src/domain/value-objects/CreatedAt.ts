import { FvDate } from './Date';
import { DateStamp } from './DateStamp';
import { ValueObject } from './ValueObject';

import type { DatePrimitive } from '../contracts/Primitives';
import type { DateTypes } from './DateStamp';

export class CreatedAt extends ValueObject<Date> {
  static build(value: DateTypes): CreatedAt {
    const stampValue = DateStamp.build(value);

    return new CreatedAt(stampValue);
  }

  static create(): CreatedAt {
    const date = FvDate.create().toPrimitive();

    return new CreatedAt(date);
  }

  toPrimitive(): DatePrimitive {
    return this.value;
  }

  toMilliseconds(): number {
    return FvDate.create(this.value).toMilliseconds();
  }

  toISO(): string {
    return FvDate.create(this.value).toISO();
  }
}
