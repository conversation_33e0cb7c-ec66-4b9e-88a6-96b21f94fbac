import { left, right } from '../contracts/Result';
import { ImageError } from '../errors/ImageError';

import { FvEnum } from './Enum';

import type { Either } from '../contracts/Result';

type MimeTypeEither = Either<ImageError, MimeType>;

export enum EMimeType {
  JPEG = 'image/jpeg',
  JPG = 'image/jpg',
  PNG = 'image/png',
  GIF = 'image/gif'
}

export class MimeType extends FvEnum<EMimeType> {
  static readonly values: EMimeType[] = Object.values(EMimeType);

  private constructor(value: EMimeType) {
    super(value, MimeType.values);
  }

  static build(value: string): MimeTypeEither {
    const newValue = value as EMimeType;

    return this.values.includes(newValue)
      ? right(new MimeType(newValue))
      : left(ImageError.invalidFormat({ context: 'MimeType', data: { value } }));
  }
}
