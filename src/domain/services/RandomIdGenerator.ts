import { init, isCuid } from '@paralleldrive/cuid2';

import { FvNumber } from '../value-objects/Number';

import { CryptoService } from './CryptoService';

export class RandomIdGenerator {
  private static readonly charsets = {
    lowerLetters: 'abcdef',
    numbers: '0123456789',
    upperLetters: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',
  };

  static create(): string {
    const length = 7;
    const charsets = this.charsets;

    let charset = `${charsets.upperLetters}${charsets.numbers}`;
    let randomString = '';

    for (let i = 0; i < length; i++) {
      const randomPoz = this.generateRandomNumber(charset.length);

      randomString += charset.substring(randomPoz, randomPoz + 1);
    }

    charset = charsets.upperLetters;

    const randomPoz = this.generateRandomNumber(charset.length);
    const randomLetter = charset.substring(randomPoz, randomPoz + 1);
    const uid = randomLetter + this.createId().toString().substring(1) + randomString;

    return uid;
  }

  static createObjectId(): string {
    const length = 8;
    const charsets = this.charsets;

    let charset = `${charsets.lowerLetters}${charsets.numbers}`;
    let randomString = '';

    for (let i = 0; i < length; i++) {
      const randomPoz = this.generateRandomNumber(charset.length);

      randomString += charset.substring(randomPoz, randomPoz + 1);
    }

    charset = charsets.lowerLetters;

    const randomHexString = CryptoService.randomBytes(length);

    const randomPoz = this.generateRandomNumber(charset.length);
    const randomLetter = charset.substring(randomPoz, randomPoz + 1);
    const uid = randomLetter + randomHexString.substring(1) + randomString;

    return uid;
  }

  static isValid(value: string): boolean {
    const uid = value.substring(0, this.createId().length);
    const validLength = value.length === this.create().length;
    const isValid = isCuid(uid) && validLength;

    return isValid || validLength;
  }

  private static createId(): string {
    return init({ length: 25 })();
  }

  private static generateRandomNumber(length: number): number {
    return FvNumber.random()
      .multiply(length)
      .floor()
      .toPrimitive();
  }
}
