import { CodeDatabaseGenerator, EDatabaseCollections } from './CodeGenerator';

import type { TicketCodeGenerator } from '../contracts/EntityCodeGenerator';
import type { FnCallback } from './CodeGenerator';

export class TicketCodeDatabaseGenerator<R> implements TicketCodeGenerator {
  constructor(private readonly fn: FnCallback<R>) { }

  async execute(sufix: string): Promise<string> {
    const options = {
      alphabetType: 1,
      length: 4,
      sufix,
    };

    const codeGenerator = new CodeDatabaseGenerator<R>(this.fn, EDatabaseCollections.TICKETS, options);

    return codeGenerator.execute();
  }
}
