import { CodeDatabaseGenerator, EDatabaseCollections } from './CodeGenerator';

import type { PassCodeGenerator } from '../contracts/EntityCodeGenerator';
import type { FnCallback } from './CodeGenerator';

export class PassCodeDatabaseGenerator<R> implements PassCodeGenerator {
  constructor(private readonly fn: FnCallback<R>) { }

  async execute(): Promise<string> {
    const codeGenerator = new CodeDatabaseGenerator<R>(this.fn, EDatabaseCollections.PASSES);

    return codeGenerator.execute();
  }
}
