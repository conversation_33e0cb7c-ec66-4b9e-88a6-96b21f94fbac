import { CryptoService } from './CryptoService';

import type { Either } from '../contracts/Result';
import type { FvError } from '../errors/FvError';

export type FnCallback<R> = (code: string) => Promise<Either<FvError | Error, R>>;
export type CodeCount<R> = Awaited<ReturnType<FnCallback<R>>>;

export type CodeOptions = {
  field?: string;
  alphabetType?: number;
  length?: number;
  sufix?: string;
  serviceDigit?: string;
};

export enum EDatabaseCollections {
  BOOKINGS = 'reservas',
  LISTS = 'listas',
  PASSES = 'passes',
  PAYLINKS = 'paylinks',
  RESERVED = 'reservados',
  TICKETS = 'entradas'
}

const CHAR_SET = {
  upperAndLowercaseAndNumbers: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789',
  uppercaseAndNumbers: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789',
};

const randomString = (length = 32, charSet = CHAR_SET.upperAndLowercaseAndNumbers): string => {
  const randomValues = new Uint8Array(length);

  CryptoService.getRandomValues(randomValues);

  let randomString = '';

  randomValues.forEach((item: number) => {
    const charSetIndex = item % charSet.length;

    randomString += charSet[charSetIndex];
  });

  return randomString;
};

const getServiceDigit = (collection: EDatabaseCollections): string => {
  const match = (collection: EDatabaseCollections): string => {
    const values: Record<EDatabaseCollections, string> = {
      [EDatabaseCollections.TICKETS]: '2',
      [EDatabaseCollections.LISTS]: '1',
      [EDatabaseCollections.PASSES]: '6',
      [EDatabaseCollections.PAYLINKS]: '5',
      [EDatabaseCollections.RESERVED]: '4',
      [EDatabaseCollections.BOOKINGS]: '3',
    };

    return collection in values ? values[collection] : 'x';
  };

  return match(collection);
};


export class CodeDatabaseGenerator<R> {
  constructor(
    private readonly fn: FnCallback<R>,
    private readonly collection: EDatabaseCollections,
    private readonly config?: CodeOptions,
  ) { }

  async execute(): Promise<string> {
    let code: string;
    let codeCount: CodeCount<R>;

    do {
      code = this.generateUniqueCode(this.collection, this.config);
      codeCount = await this.fn(code);
    } while (codeCount.isRight());

    return code;
  }

  private generateUniqueCode(collection: EDatabaseCollections, config?: CodeOptions): string {
    const defaultConfig: Required<CodeOptions> = {
      field: 'codigo',
      alphabetType: 1,
      length: 4,
      sufix: '',
      serviceDigit: config?.serviceDigit ?? getServiceDigit(collection),
    };

    const options = { ...defaultConfig, ...config };
    const alphabet = options.alphabetType === 0 ? CHAR_SET.upperAndLowercaseAndNumbers : CHAR_SET.uppercaseAndNumbers;

    if (collection === EDatabaseCollections.PASSES) {
      options.sufix = randomString(options.length, alphabet);
    }

    const code = randomString(options.length, alphabet);

    return `${options.sufix}${options.serviceDigit}${code}`;
  };
}
