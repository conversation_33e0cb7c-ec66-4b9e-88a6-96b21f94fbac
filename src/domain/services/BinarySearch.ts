import { FvNumber } from '../value-objects/Number';
import { FvString } from '../value-objects/String';

type CompareFn<T> = (a: T, b: T) => number;

export class BinarySearch {
  static search<T>(list: T[], target: T, compareFn?: CompareFn<T>): number {
    const requiredCompareFn = this.makeCompareFn<T>(target, compareFn);

    const start = FvNumber.createZero();
    const end = FvNumber.build(list.length - 1);

    return this.recursiveSearch<T>(list, target, start, end, requiredCompareFn);
  }

  private static makeCompareFn<T>(target: T, compareFn?: CompareFn<T>): CompareFn<T> {
    if (compareFn) {
      return compareFn;
    }

    if (FvNumber.is(target)) {
      return (a, b) => {
        const numberA = a as unknown as number;
        const numberB = b as unknown as number;

        return FvNumber.build(numberA).compareTo(numberB);
      };
    }

    return (a, b) => {
      const stringA = a as unknown as string;
      const stringB = b as unknown as string;

      return FvString.build(stringA).compareTo(stringB);
    };
  }

  private static recursiveSearch<T>(
    list: T[],
    target: T,
    start: FvNumber,
    end: FvNumber,
    compareFn: CompareFn<T>,
  ): number {
    if (start.isGreaterThan(end)) {
      return -1;
    }

    const fvMiddleIndex = start
      .add(end)
      .divide(2)
      .floor();

    const middleValue = list[fvMiddleIndex.toPrimitive()];

    if (!middleValue) {
      return -1;
    }

    const comparison = FvNumber.build(compareFn(target, middleValue));

    if (comparison.isZero()) {
      return fvMiddleIndex.toPrimitive();
    }

    if (comparison.isNegative()) {
      const newEnd = fvMiddleIndex.subtract(1);

      return this.recursiveSearch(list, target, start, newEnd, compareFn);
    }

    const newStart = fvMiddleIndex.add(1);

    return this.recursiveSearch(list, target, newStart, end, compareFn);
  }
}
