import { UUID } from '../value-objects/UUID';

import type { Optional, UnknownObject } from '../contracts/Generics';

type MemoryUsageInterval = {
  start: NodeJS.MemoryUsage | null;
  end: NodeJS.MemoryUsage | null;
};

type MemoryUsageResponse = {
  readonly rss: string;
  readonly heapTotal: string;
  readonly heapUsed: string;
  readonly external: string;
};

type MeasureResponse = Optional<{
  readonly name: string;
  readonly startTime: string;
  readonly duration: string;
  readonly start: MemoryUsageResponse;
  readonly end: MemoryUsageResponse;
}>;

export class PerformanceMeasurement {
  private readonly startMark: string;
  private readonly endMark: string;
  private readonly measureName: string;

  private readonly memoryUsageInterval: MemoryUsageInterval = {
    start: null,
    end: null,
  };

  private measure: PerformanceEntry | null = null;

  private constructor(measureName: string) {
    this.measureName = `${measureName}-${UUID.create().toPrimitive()}`;
    this.startMark = `${measureName}-Start`;
    this.endMark = `${measureName}-End`;
  }

  static create(measureName: string): PerformanceMeasurement {
    const measurement = new PerformanceMeasurement(measureName);

    measurement.start();
    measurement.memoryUsageInterval.start = process.memoryUsage();

    return measurement;
  }

  start(): void {
    performance.mark(this.startMark);
  }

  end(): number {
    this.memoryUsageInterval.end = process.memoryUsage();

    if (!performance.getEntriesByName(this.startMark, 'mark').length) {
      return 0;
    }

    performance.mark(this.endMark);
    performance.measure(this.measureName, this.startMark, this.endMark);

    const [measure] = performance.getEntriesByName(this.measureName, 'measure');

    if (measure) {
      this.measure = measure;
    }

    performance.clearMarks();
    performance.clearMeasures();

    return this.getDurationMs();
  }

  private getDurationMs(): number {
    if (!this.measure) {
      return 0;
    }

    return +this.measure.duration.toFixed(2);
  }

  toJSON(): UnknownObject {
    const convert = (value: number): string => (value / 1024 / 1024).toFixed(2);
    let response: MeasureResponse = {};

    if (this.measure) {
      const measure = {
        name: `${this.measure.name}`,
        startTime: `${this.measure.startTime.toFixed(2)} ms`,
        duration: `${this.getDurationMs()} ms`,
      };

      response = { ...response, ...measure };
    }

    if (this.memoryUsageInterval.start) {
      const start = this.memoryUsageInterval.start;

      const startInterval = {
        rss: `${convert(start.rss)} MB - (Resident Set Size)`,
        heapTotal: `${convert(start.heapTotal)} MB - (Total heap allocated)`,
        heapUsed: `${convert(start.heapUsed)} MB - (Heap actually used)`,
        external: `${convert(start.external)} MB - (External memory)`,
      };

      response = { ...response, start: startInterval };
    }

    if (this.memoryUsageInterval.end) {
      const end = this.memoryUsageInterval.end;

      const endInterval = {
        rss: `${convert(end.rss)} MB - (Resident Set Size)`,
        heapTotal: `${convert(end.heapTotal)} MB - (Total heap allocated)`,
        heapUsed: `${convert(end.heapUsed)} MB - (Heap actually used)`,
        external: `${convert(end.external)} MB - (External memory)`,
      };

      response = { ...response, end: endInterval };
    }

    return response;
  }
}
