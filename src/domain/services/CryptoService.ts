import { randomBytes } from '@noble/hashes/utils';
import CryptoJS from 'crypto-js';

import { FvNumber } from '../value-objects/Number';

export class CryptoService {
  static createHash(value: string, algorithm: string): string {
    algorithm = algorithm.toUpperCase();

    const match = {
      'SHA-256': CryptoJS.SHA256(value).toString(),
      'SHA-1': CryptoJS.SHA1(value).toString(),
      'MD5': CryptoJS.MD5(value).toString(),
    };

    if (algorithm in match) {
      return match[algorithm as keyof typeof match];
    }

    throw new Error(`Unsupported hash algorithm: ${algorithm}`);
  }

  static async sha256(input: string): Promise<string> {
    const hashedValue = await this.algorithmEncoding(input, 'SHA-256');

    return hashedValue;
  }

  static randomBytes(length: number): string {
    return randomBytes(length).toString();
  }

  static getRandomValues<T extends ArrayBufferView>(typedArray: T): T {
    const bytes = randomBytes(typedArray.byteLength);

    new Uint8Array(typedArray.buffer).set(bytes);

    return typedArray;
  }


  private static async algorithmEncoding(input: string, algorithm: string): Promise<string> {
    const encoder = new TextEncoder();
    const encodedData = encoder.encode(input);
    const hashBuffer = await crypto.subtle.digest(algorithm, encodedData);

    const hashArray = new Uint8Array(hashBuffer);
    const hashedValue = Array.from(hashArray).map((byte) => {
      return byte
        .toString(16)
        .padStart(2, '0');
    }).join('');

    return hashedValue;
  }

  static hexToBase62(hex: string): string {
    const decimalNumber = parseInt(hex, 16);

    const base62Chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    let response = '';

    let quotient = FvNumber.build(decimalNumber);

    while (quotient.isPositive()) {
      const remainder = quotient.mod(62);

      response = base62Chars.charAt(remainder.toPrimitive()) + response;
      quotient = quotient.divide(62).floor();
    }

    return response || '0';
  }
}
