import { FvError } from './FvError';

import type { ErrorMethodRequest } from './FvError';

export class CheckError extends FvError {
  static readonly defaultCause = 'invalid_field';

  static build(request: ErrorMethodRequest): CheckError {
    const {
      context, error, data, target,
    } = request;

    const defaultMessage = `Invalid field ${context}`;

    return new this(
      defaultMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }
}
