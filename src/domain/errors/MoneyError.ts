import { FvError } from './FvError';

import type { ErrorMethodRequest, FvErrorRequest } from './FvError';

export class MoneyError extends FvError {
  static readonly cause = 'invalid_money';

  static build(request: FvErrorRequest): MoneyError {
    const exceptionMessage = 'Money error';

    const {
      message, context, data, error, target,
    } = request;

    return new this(
      message ?? exceptionMessage,

      context,
      this.cause,
      error,
      data,
      target,
    );
  }

  static incompatibleCurrencies(request: ErrorMethodRequest): MoneyError {
    const exceptionMessage = 'Currencies must match';

    const {
      context, data, error, target,
    } = request;

    return new this(
      exceptionMessage,
      context,
      this.cause,
      error,
      data,
      target,
    );
  }

  static currencyNotSupported(request: ErrorMethodRequest): MoneyError {
    const exceptionMessage = 'Currency not supported';

    const {
      context, data, error, target,
    } = request;

    return new this(
      exceptionMessage,
      context,
      this.cause,
      error,
      data,
      target,
    );
  }
}
