import type { UnknownObject } from '../contracts/Generics';

export type ErrorContext = {
  readonly context: string;
  readonly data?: unknown;
  readonly errors: unknown;
};

export type FvErrorRequest<DATA = UnknownObject, TARGET = unknown> = {
  readonly context: string;
  readonly message?: string;
  readonly error?: Error;
  readonly data?: DATA;
  readonly target?: TARGET;
};

export type ErrorMethodRequest = Omit<FvErrorRequest, 'message'>;

export type FvErrorContextualizeDto = Pick<FvErrorRequest, 'context' | 'error' | 'data'>;

export type FvErrorStack = Omit<FvErrorRequest, 'error'> & {
  step?: number;
  readonly error?: string;
};

export abstract class FvError extends Error {
  private errorStack: FvErrorStack[] = [];
  public isAutoContextualizable: boolean = true;

  protected constructor(
    readonly message: string,
    readonly context: string,
    private _cause: string,
    private _error?: Error,
    private _data?: UnknownObject,
    private readonly target?: unknown,
  ) {
    super(message);

    this.addErrorStack({
      message,
      context,
      target,
      data: _data,
      error: _error,
    });
  }

  /**
   * @example
   * const myError = NotFoundError.build({
   *  context: this.constructor.name,
   *  data: {eventId: "1234"}
   * });
   *
   * const myNewError = myErro.contextualize({
   *  context: "a-higher-context",
   *  data: {"some": "new-data"}
   * });
   */
  contextualize({
    context, data, error,
  }: FvErrorContextualizeDto): this {
    const ErrorClass = this.constructor as new (
      message: string,
      context: string,
      cause: string,
      error?: Error,
      data?: object,
      target?: unknown,
    ) => this;

    const clone = new ErrorClass(
      this.message,
      context,
      this.cause,
      error,
      data,
      this.target,
    );

    clone.setErrorStack(this.errorStack);

    clone.addErrorStack({
      context,
      data,
      error,
    });

    return clone;
  }

  /**
   * This is used to show the error stack in production platforms like rollbar or google cloud logs.
   */
  getStack(): FvErrorStack[] {
    return this.errorStack;
  }

  /**
   * This should be only shown on local environment.
   */
  toString(): string {
    let relevantMessage = `${this.message} | context: ${this.context}`;

    if (this.error) {
      relevantMessage = `${relevantMessage} | error: ${this.error}`;
    }

    if (this.target) {
      relevantMessage += ` | target: ${JSON.stringify(this.target)}`;
    }

    if (this.data && Object.keys(this.data).length > 0) {
      relevantMessage += ` | data: ${JSON.stringify(this.data)}`;
    }

    return relevantMessage;
  }

  private setErrorStack(errorStack: FvErrorStack[]): void {
    this.errorStack = errorStack;
  }

  private addErrorStack(errorStack: FvErrorRequest): void {
    this.errorStack.unshift({
      ...errorStack,
      error: errorStack.error?.toString(),
    });

    for (let i = 0; i < this.errorStack.length; i++) {
      const errorStackItem = this.errorStack[i];

      if (!errorStackItem) {
        break;
      }

      errorStackItem.step = i + 1;
    }
  }

  get error(): Error | undefined {
    return this._error;
  }

  get data(): object | undefined {
    return this._data;
  }

  get cause(): string {
    return this._cause;
  }

  setCause(cause: string): this {
    this._cause = cause;

    return this;
  }

  setError(error: Error): this {
    this._error = error;

    return this;
  }

  setData(data: UnknownObject): this {
    this._data = data;

    return this;
  }

  /**
   * This is used to disable automatic use of `.contextualize(...)` of the error in decorators or other non-manual processes.
   */
  notAutoContextualizable(): this {
    this.isAutoContextualizable = false;

    return this;
  }
}
