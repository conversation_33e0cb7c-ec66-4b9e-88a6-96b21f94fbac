import { FvError } from './FvError';

import type { ErrorMethodRequest } from './FvError';

export class NotFoundError extends FvError {
  static readonly defaultCause = 'entity_not_found';

  /**
   * @example
   * const myError = NotFoundError.build({
   *  context: this.constructor.name,
   *  target: EventEntity.name,
   *  data: { id: 1 },
   * })
   */
  static build(request: ErrorMethodRequest): NotFoundError {
    const {
      context, error, data, target,
    } = request;

    const exceptionMessage = `${target ?? 'Entity'} not found`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }
}
