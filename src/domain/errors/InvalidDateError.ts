import { FvError } from './FvError';

import type { ErrorMethodRequest } from './FvError';
import type { InvalidArgumentError } from './InvalidArgumentError';

export class InvalidDateError extends FvError {
  static readonly defaultCause = 'invalid_date';

  static build(request: ErrorMethodRequest): InvalidArgumentError {
    const exceptionMessage = `Invalid Date`;

    const {
      context, data, error, target,
    } = request;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }
}
