import { FvError } from './FvError';

import type { ErrorMethodRequest } from './FvError';

export class ImageError extends FvError {
  static readonly defaultCause = 'extension_not_allowed';

  static invalidFormat(request: ErrorMethodRequest): ImageError {
    const exceptionMessage = `Invalid image format`;

    const {
      context, data, error, target,
    } = request;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static invalidExtension(request: ErrorMethodRequest): ImageError {
    const exceptionMessage = `Invalid image extension`;

    const {
      context, data, error, target,
    } = request;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }
}
