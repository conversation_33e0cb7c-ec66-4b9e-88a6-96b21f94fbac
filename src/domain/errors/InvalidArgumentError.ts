import { FvError } from '../errors/FvError';

import type { ErrorMethodRequest, FvErrorRequest } from '../errors/FvError';

export class InvalidArgumentError extends FvError {
  static readonly defaultCause = 'invalid_field';

  static build(request: FvErrorRequest): InvalidArgumentError {
    const {
      message, context, error, data, target,
    } = request;

    const exceptionMessage = `Invalid argument error`;

    return new this(
      message ?? exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static filterOperator(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `The filter operator ${target} is invalid`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static currencyNotSupported(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `Currency ${target} not supported`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static amountCannotBeNegative(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;


    const exceptionMessage = 'Amount cannot be negative.';

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static invalidDocumentType(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `The document type ${target} is invalid`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static invalidDocumentNumber(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `The document number ${target} is invalid`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static invalidPhone(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `Invalid phone: ${target}`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static invalidOrderType(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `The order type ${target} is invalid`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static invalidTicketTypeDisponibility(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `The ticketType disponibility ${target} is invalid`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static invalidGuestListRateType(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `The guestList rateType ${target} is invalid`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static invalidPaylinkState(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `The paylink state ${target} is invalid`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static invalidReservationState(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `The reservation state ${target} is invalid`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static invalidTicketState(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `The ticket state ${target} is invalid`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static invalidTicketTypeRateType(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `The ticketType rateType ${target} is invalid`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static invalidNotificationType(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `The notification type ${target} is invalid`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static purchaseMinCannotBeGreaterThanMax(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `The purchase min ${target} cannot be greater than max`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static purchaseUnlimitedCannotBeTrueIfMaxIsProvided(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `The purchase unlimited ${target} cannot be true if max is provided`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static purchaseQuantityIsLessThanMin(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `The purchase quantity ${target} is less than min`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static purchaseQuantityIsGreaterThanMax(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `The purchase quantity ${target} is greater than max`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static buildInvalidPaymentType(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `The payment type ${target} is invalid`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static buildInvalidPaymentMethod(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `The payment method ${target} is invalid`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static buildInvalidPaymentState(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `The payment state ${target} is invalid`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static buildInvalidPaymentChannel(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `The payment channel ${target} is invalid`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static buildInvalidAdministrationFeeType(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `The administration fee type ${target} is invalid`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static buildInvalidAdministrationFeeAmount(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `The administration fee amount ${target} is invalid for the type`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static buildInvalidReservationState(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `The reservation state ${target} is invalid`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static buildInvalidPricingType(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `The deposit type ${target} is invalid`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }

  static invalidValue(request: ErrorMethodRequest): InvalidArgumentError {
    const {
      context, data, error, target,
    } = request;

    const exceptionMessage = `Value "${target}" is invalid`;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }
}

