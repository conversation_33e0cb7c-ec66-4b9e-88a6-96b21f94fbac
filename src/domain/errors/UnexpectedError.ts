import { FvError } from './FvError';

import type { ErrorMethodRequest } from './FvError';

export class UnexpectedError extends FvError {
  static readonly defaultCause = 'unexpected_error';

  static build(request: ErrorMethodRequest): UnexpectedError {
    const exceptionMessage = `Unexpected error`;

    const {
      context, error, data, target,
    } = request;

    return new this(
      exceptionMessage,
      context,
      this.defaultCause,
      error,
      data,
      target,
    );
  }
}
