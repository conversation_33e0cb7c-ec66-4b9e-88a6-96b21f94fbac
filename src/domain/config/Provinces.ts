export type ProvinceConfig = {
  readonly province: string;
  readonly community: string;
  readonly codeAmchart: string;
  readonly zipCode: string;
};

type ProvincessConfig = Record<string, ProvinceConfig>;

const provincesObjectConfig: ProvincessConfig = {
  '01': {
    province: 'Álava',
    community: '<PERSON><PERSON>',
    codeAmchart: 'ES-PV',
    zipCode: '01',
  },
  '02': {
    province: 'Albacete',
    community: 'Castilla-La Mancha',
    codeAmchart: 'ES-CM',
    zipCode: '02',
  },
  '03': {
    province: 'Alicante',
    community: 'Comunidad Valenciana',
    codeAmchart: 'ES-VC',
    zipCode: '03',
  },
  '04': {
    province: 'Almería',
    community: 'Andalucía',
    codeAmchart: 'ES-AN',
    zipCode: '04',
  },
  '05': {
    province: 'Ávila',
    community: 'Castilla y León',
    codeAmchart: 'ES-CL',
    zipCode: '05',
  },
  '06': {
    province: 'Badajoz',
    community: 'Extremadura',
    codeAmchart: 'ES-EX',
    zipCode: '06',
  },
  '07': {
    province: 'Baleares',
    community: 'Baleares',
    codeAmchart: 'ES-IB',
    zipCode: '07',
  },
  '08': {
    province: 'Barcelona',
    community: 'Cataluña',
    codeAmchart: 'ES-CT',
    zipCode: '08',
  },
  '09': {
    province: 'Burgos',
    community: 'Castilla y León',
    codeAmchart: 'ES-CL',
    zipCode: '09',
  },
  '10': {
    province: 'Cáceres',
    community: 'Extremadura',
    codeAmchart: 'ES-EX',
    zipCode: '10',
  },
  '11': {
    province: 'Cádiz',
    community: 'Andalucía',
    codeAmchart: 'ES-AN',
    zipCode: '11',
  },
  '12': {
    province: 'Castellón',
    community: 'Comunidad Valenciana',
    codeAmchart: 'ES-VC',
    zipCode: '12',
  },
  '13': {
    province: 'Ciudad Real',
    community: 'Castilla-La Mancha',
    codeAmchart: 'ES-CM',
    zipCode: '13',
  },
  '14': {
    province: 'Córdoba',
    community: 'Andalucía',
    codeAmchart: 'ES-AN',
    zipCode: '14',
  },
  '15': {
    province: 'Coruña',
    community: 'Galicia',
    codeAmchart: 'ES-GA',
    zipCode: '15',
  },
  '16': {
    province: 'Cuenca',
    community: 'Castilla-La Mancha',
    codeAmchart: 'ES-CM',
    zipCode: '16',
  },
  '17': {
    province: 'Gerona',
    community: 'Cataluña',
    codeAmchart: 'ES-CT',
    zipCode: '17',
  },
  '18': {
    province: 'Granada',
    community: 'Andalucía',
    codeAmchart: 'ES-AN',
    zipCode: '18',
  },
  '19': {
    province: 'Guadalajara',
    community: 'Castilla-La Mancha',
    codeAmchart: 'ES-CM',
    zipCode: '19',
  },
  '20': {
    province: 'Guipúzcoa',
    community: 'País Vasco',
    codeAmchart: 'ES-PV',
    zipCode: '20',
  },
  '21': {
    province: 'Huelva',
    community: 'Andalucía',
    codeAmchart: 'ES-AN',
    zipCode: '21',
  },
  '22': {
    province: 'Huesca',
    community: 'Aragón',
    codeAmchart: 'ES-AR',
    zipCode: '22',
  },
  '23': {
    province: 'Jaén',
    community: 'Andalucía',
    codeAmchart: 'ES-AN',
    zipCode: '23',
  },
  '24': {
    province: 'León',
    community: 'Castilla y León',
    codeAmchart: 'ES-CL',
    zipCode: '24',
  },
  '25': {
    province: 'Lérida',
    community: 'Cataluña',
    codeAmchart: 'ES-CT',
    zipCode: '25',
  },
  '26': {
    province: 'La Rioja',
    community: 'La Rioja',
    codeAmchart: 'ES-RI',
    zipCode: '26',
  },
  '27': {
    province: 'Lugo',
    community: 'Galicia',
    codeAmchart: 'ES-GA',
    zipCode: '27',
  },
  '28': {
    province: 'Madrid',
    community: 'Madrid',
    codeAmchart: 'ES-MD',
    zipCode: '28',
  },
  '29': {
    province: 'Málaga',
    community: 'Andalucía',
    codeAmchart: 'ES-AN',
    zipCode: '29',
  },
  '30': {
    province: 'Murcia',
    community: 'Murcia',
    codeAmchart: 'ES-MC',
    zipCode: '30',
  },
  '31': {
    province: 'Navarra',
    community: 'Navarra',
    codeAmchart: 'ES-NC',
    zipCode: '31',
  },
  '32': {
    province: 'Orense',
    community: 'Galicia',
    codeAmchart: 'ES-GA',
    zipCode: '32',
  },
  '33': {
    province: 'Asturias',
    community: 'Asturias',
    codeAmchart: 'ES-AS',
    zipCode: '33',
  },
  '34': {
    province: 'Palencia',
    community: 'Castilla y León',
    codeAmchart: 'ES-CL',
    zipCode: '34',
  },
  '35': {
    province: 'Las Palmas',
    community: 'Islas Canarias',
    codeAmchart: 'ES-CN',
    zipCode: '35',
  },
  '36': {
    province: 'Pontevedra',
    community: 'Galicia',
    codeAmchart: 'ES-GA',
    zipCode: '36',
  },
  '37': {
    province: 'Salamanca',
    community: 'Castilla y León',
    codeAmchart: 'ES-CL',
    zipCode: '37',
  },
  '38': {
    province: 'Santa Cruz de Tenerife',
    community: 'Islas Canarias',
    codeAmchart: 'ES-CN',
    zipCode: '38',
  },
  '39': {
    province: 'Cantabria',
    community: 'Cantabria',
    codeAmchart: 'ES-CB',
    zipCode: '39',
  },
  '40': {
    province: 'Segovia',
    community: 'Castilla y León',
    codeAmchart: 'ES-CL',
    zipCode: '40',
  },
  '41': {
    province: 'Sevilla',
    community: 'Andalucía',
    codeAmchart: 'ES-AN',
    zipCode: '41',
  },
  '42': {
    province: 'Soria',
    community: 'Castilla y León',
    codeAmchart: 'ES-CL',
    zipCode: '42',
  },
  '43': {
    province: 'Tarragona',
    community: 'Cataluña',
    codeAmchart: 'ES-CT',
    zipCode: '43',
  },
  '44': {
    province: 'Teruel',
    community: 'Aragón',
    codeAmchart: 'ES-AR',
    zipCode: '44',
  },
  '45': {
    province: 'Toledo',
    community: 'Castilla-La Mancha',
    codeAmchart: 'ES-CM',
    zipCode: '45',
  },
  '46': {
    province: 'Valencia',
    community: 'Comunidad Valenciana',
    codeAmchart: 'ES-VC',
    zipCode: '46',
  },
  '47': {
    province: 'Valladolid',
    community: 'Castilla y León',
    codeAmchart: 'ES-CL',
    zipCode: '47',
  },
  '48': {
    province: 'Vizcaya',
    community: 'País Vasco',
    codeAmchart: 'ES-PV',
    zipCode: '48',
  },
  '49': {
    province: 'Zamora',
    community: 'Castilla y León',
    codeAmchart: 'ES-CL',
    zipCode: '49',
  },
  '50': {
    province: 'Zaragoza',
    community: 'Aragón',
    codeAmchart: 'ES-AR',
    zipCode: '50',
  },
  '51': {
    province: 'Ceuta',
    community: 'Ceuta',
    codeAmchart: '',
    zipCode: '52',
  },
  '52': {
    province: 'Melilla',
    community: '',
    codeAmchart: '',
    zipCode: '52',
  },
};

export const provincesConfig = new Map<string, ProvinceConfig>();

for (const [zipCode, config] of Object.entries(provincesObjectConfig)) {
  provincesConfig.set(zipCode, config);
}
