import type { Handler } from './AbstractHandler';

interface IResponsability {
  execute: <I, O>(request: I) => Promise<O>;
}

export const Responsability = (handlers: Handler[]): IResponsability => {
  const execute = async <I, O>(request: I): Promise<O> => {
    const iterator = [...new Set(handlers)];

    for (let i = 0; i < iterator.length - 1; i++) {
      const handler = iterator[i];
      const nextHandler = iterator[i + 1];

      if (handler && nextHandler) {
        handler.next(nextHandler);
      }
    }

    const first = handlers.values().next().value as Handler;

    return await first.handle<O>(request);
  };

  return { execute };
};
