/* eslint-disable @typescript-eslint/no-explicit-any */

export interface Handler {
  next: (handler: <PERSON><PERSON>) => Handler;
  handle: <T>(request: any) => Promise<T>;
  finish: <T>(request: any) => Promise<T>;
}

export abstract class AbstractHandler implements Handler {
  private nextHandler: Handler | null = null;

  next(handler: <PERSON><PERSON>): Handler {
    this.nextHandler = handler;

    return handler;
  }

  async handle<T>(request: unknown): Promise<T> {
    if (this.nextHandler) {
      return await this.nextHandler.handle(request);
    }

    return this.finish(request);
  }

  async finish<T>(request: unknown): Promise<T> {
    this.nextHandler = null;

    return request as T;
  }
}
