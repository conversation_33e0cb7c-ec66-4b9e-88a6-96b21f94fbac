import { right } from '@/domain/contracts/Result';

import { Component } from './Component';

import type { Either } from '@/domain/contracts/Result';
import type { CheckError } from '@/domain/errors/CheckError';

export class ValidatorComposite extends Component {
  protected children: Component[] = [];

  static build(): ValidatorComposite {
    return new ValidatorComposite();
  }

  add(component: Component): this {
    this.children.push(component);
    component.setParent(this);

    return this;
  }

  remove(component: Component): void {
    const componentIndex = this.children.indexOf(component);

    this.children.splice(componentIndex, 1);

    component.setParent(null);
  }

  execute(request: unknown): Either<CheckError, boolean> {
    if (this.children.length === 0) {
      return right(true);
    }

    for (const child of this.children) {
      const childResult = child.execute(request);

      if (childResult.isLeft()) {
        return childResult;
      }
    }

    return right(true);
  }
}
