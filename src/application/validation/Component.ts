import type { Either } from '@/domain/contracts/Result';
import type { CheckError } from '@/domain/errors/CheckError';

export abstract class Component {
  protected parent!: Component | null;

  setParent(parent: Component | null): Component {
    this.parent = parent;

    return this;
  }

  getParent(): Component | null {
    return this.parent;
  }

  abstract execute(request: unknown): Either<CheckError, boolean>;
}
