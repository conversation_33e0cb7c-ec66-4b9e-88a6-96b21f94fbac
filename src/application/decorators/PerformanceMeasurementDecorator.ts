import { left } from '@/domain/contracts/Result';
import { UnexpectedError } from '@/domain/errors/UnexpectedError';
import { PerformanceMeasurement } from '@/domain/services/PerformanceMeasurement';

import { Decorator } from './Decorator';

import type { UnknownObject } from '@/domain/contracts/Generics';
import type { Logger } from '@/domain/contracts/Logger';
import type { UseCase } from '../contracts/UseCase';

export class PerformanceMeasurementDecorator<I, O> extends Decorator<I, O> {
  constructor(
    private readonly name: string,
    private readonly logger: Logger,
    component: UseCase<I, Promise<O>>,
  ) {
    super(component);
  }

  async execute(dto: I): Promise<O> {
    const performanceMeasurement = PerformanceMeasurement.create(this.name);

    let hasStarted = false;

    try {
      hasStarted = true;

      return await super.execute(dto);
    } catch (error) {
      const castedError = error as Error;

      return left(UnexpectedError.build({
        context: this.name,
        error: castedError,
        data: dto as unknown as UnknownObject,
      })) as O;
    } finally {
      if (hasStarted) {
        performanceMeasurement.end();

        this.logger.debug(performanceMeasurement.toJSON());
      }
    }
  }
}
