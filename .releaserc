{"branches": ["master"], "plugins": [["@semantic-release/commit-analyzer", {"preset": "conventionalcommits", "releaseRules": [{"type": "refactor", "release": "patch"}]}], "@semantic-release/release-notes-generator", "@semantic-release/changelog", ["@semantic-release/npm", {"npmPublish": false, "tarballDir": "dist"}], ["@semantic-release/git", {"message": "chore(release): ${nextRelease.version} \n\n${nextRelease.notes}"}]]}