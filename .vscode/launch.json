{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Docker: Attach to Node",
      "type": "node",
      "request": "attach",
      "port": 9231,
      "sourceMaps": true,
      "localRoot": "${workspaceFolder}",
      "remoteRoot": "/usr/src/app",
      "smartStep": true,
      "restart": true,
      "trace": true,
      "sourceMapPathOverrides": {
        "/usr/src/app/*": "${workspaceFolder}/*"
      },
      "skipFiles": [
        "<node_internals>/**",
        "node_modules/**"
      ]
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Current File",
      "program": "${workspaceFolder}/node_modules/.bin/jest",
      "args": [
        "--runTestsByPath",
        "${relativeFile}",
      ],
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "windows": {
        "program": "${workspaceFolder}/node_modules/jest/bin/jest",
      }
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Jest (Unit) Current File",
      "program": "${workspaceFolder}/node_modules/.bin/jest",
      "args": [
        "--runTestsByPath",
        "${relativeFile}",
        "--config",
        "jest.config.ts"
      ],
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen",
      "runtimeExecutable": "${env:HOME}/.nvm/versions/node/v22.12.0/bin/node",
      "windows": {
        "program": "${workspaceFolder}/node_modules/jest/bin/jest",
      }
    },
  ]
}