{"compilerOptions": {"baseUrl": ".", "target": "ES2024", "lib": ["ESNext", "DOM"], "module": "NodeNext", "moduleResolution": "NodeNext", "types": ["node", "jest"], "resolveJsonModule": true, "sourceMap": true, "outDir": "dist", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictPropertyInitialization": true, "noImplicitThis": true, "useUnknownInCatchVariables": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "skipLibCheck": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "declaration": true, "declarationMap": true, "isolatedModules": true, "paths": {"@/*": ["src/*"], "@tests/*": ["tests/*"]}}, "include": ["src/**/*.ts", "tests/**/*.ts", "jest.config.ts"], "exclude": ["node_modules", "dist"]}