import { faker } from '@faker-js/faker';

import type {
  Faker, FoodModule, LoremModule, NumberModule, PersonModule,
} from '@faker-js/faker';

export class MotherCreator {
  static random(): Faker {
    return faker;
  }

  static number(): NumberModule {
    return faker.number;
  }

  static uuid(): string {
    return faker.string.uuid();
  }

  static person(): PersonModule {
    return faker.person;
  }

  static lorem(): LoremModule {
    return faker.lorem;
  }

  static food(): FoodModule {
    return faker.food;
  }
}
