import { CreatedAt } from '@/domain/value-objects/CreatedAt';
import { FvDate } from '@/domain/value-objects/Date';

describe(`${CreatedAt.name}`, () => {
  const date = FvDate.create();

  it('should return date primitive', () => {
    const createdAtByDate = CreatedAt.build(date.toPrimitive());

    expect(date.toPrimitive()).toStrictEqual(createdAtByDate.toPrimitive());
  });

  it('should return iso  date', () => {
    const isoDate = date.toISO();
    const createdAtByISO = CreatedAt.build(isoDate);

    expect(date.toPrimitive()).toStrictEqual(createdAtByISO.toPrimitive());
  });

  it('should return date in milliseconds', () => {
    const millisecondDate = date.toMilliseconds();
    const createdAtByMs = CreatedAt.build(millisecondDate);

    expect(date.toPrimitive()).toStrictEqual(createdAtByMs.toPrimitive());
  });

  it('should return the current date when the iso date is incorrect', () => {
    const badIsoDate = '2025-08-05L07:45:01.620Z';
    const createdAtByMs = CreatedAt.build(badIsoDate);

    expect(date.toMilliseconds()).toBeLessThan(createdAtByMs.toMilliseconds());
  });
});
