import { FvDate } from '@/domain/value-objects/Date';
import { RemovedAt } from '@/domain/value-objects/RemovedAt';

describe(`${RemovedAt.name}`, () => {
  const date = FvDate.create();

  it('should return date primitive', () => {
    const removedAtByDate = RemovedAt.build(date.toPrimitive());
    const removedAtPrimitive = removedAtByDate.toPrimitive();

    expect(removedAtPrimitive.isDefined()).toBeTruthy();
    expect(date.toPrimitive()).toStrictEqual(removedAtPrimitive.get());
  });

  it('should return iso  date', () => {
    const isoDate = date.toISO();
    const removedAtByISO = RemovedAt.build(isoDate);
    const removedAtPrimitive = removedAtByISO.toPrimitive();

    expect(removedAtPrimitive.isDefined()).toBeTruthy();
    expect(date.toPrimitive()).toStrictEqual(removedAtPrimitive.get());
  });

  it('should return date in milliseconds', () => {
    const millisecondDate = date.toMilliseconds();
    const removedAtByMs = RemovedAt.build(millisecondDate);
    const removedAtPrimitive = removedAtByMs.toPrimitive();

    expect(removedAtPrimitive.isDefined()).toBeTruthy();
    expect(date.toPrimitive()).toStrictEqual(removedAtPrimitive.get());
  });

  it('should return empty date', () => {
    const removedAtNull = RemovedAt.build(null);
    const removedAtPrimitive = removedAtNull.toPrimitive();

    expect(removedAtPrimitive.isEmpty()).toBeTruthy();
  });

  it('should return the current date when the iso date is incorrect', () => {
    const badIsoDate = '2025-08-05L07:45:01.620Z';
    const removedAtByMs = RemovedAt.build(badIsoDate);

    expect(date.toMilliseconds()).toBeLessThan(removedAtByMs.toMilliseconds());
  });
});
