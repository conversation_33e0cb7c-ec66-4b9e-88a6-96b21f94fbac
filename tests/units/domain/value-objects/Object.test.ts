import { FvObject } from '@/domain/value-objects/Object';

describe(`${FvObject.name}`, () => {
  test('getOrElse', () => {
    const adyenResponse = { SaleToPOIResponse: { PaymentResponse: { PaymentResult: { PaymentAcquirerData: { AcquirerTransactionID: { TransactionID: 'ABC123' } } } } } };

    const basePath
      = 'SaleToPOIResponse.PaymentResponse.PaymentResult.PaymentAcquirerData.AcquirerTransactionID';
    const correctPath = `${basePath}.TransactionID`;
    const badPath = `${basePath}.TransactionUUID`;
    const badPath2 = `${basePath}-TransactionID`;
    const badPath3 = `SaleToPOIResponse.PaymentResponse.PaymentResult22.PaymentAcquirerData.AcquirerTransactionID`;

    const fvAdyen = FvObject.build(adyenResponse);
    const result1 = fvAdyen.getOrElse(correctPath, 'result1-default'); // Path correcto
    const result2 = fvAdyen.getOrElse(badPath, 'result2-default'); // Ultimo item del path incorrecto
    const result3 = fvAdyen.getOrElse(`${basePath}`, 'result3-default'); // Path correcto, que devuelve un objeto
    const result4 = fvAdyen.getOrElse(badPath2, 'result4-default'); // Ultima key del path incorrecta
    const result5 = fvAdyen.getOrElse(`${basePath}.TransactionUUID`); // Ultimo item del path incorrecto, sin default
    const result6 = fvAdyen.getOrElse(badPath3, 'result6-default'); // Path incorrecto
    const result7 = fvAdyen.getOrElse('hola-chau', 'result7-default');
    const result8 = fvAdyen.getOrElse('12345', 123);
    const result9 = fvAdyen.getOrElse('boolean', true);
    const result10 = FvObject.getOrElse(null, 'boolean', 123);
    const result11 = FvObject.getOrElse(undefined, 'boolean', 123);

    expect(result1).toStrictEqual('ABC123');
    expect(result2).toStrictEqual('result2-default');
    expect(result3).toStrictEqual({ TransactionID: 'ABC123' });
    expect(result4).toStrictEqual('result4-default');
    expect(result5).toBeUndefined();
    expect(result6).toStrictEqual('result6-default');
    expect(result7).toStrictEqual('result7-default');
    expect(result8).toStrictEqual(123);
    expect(result9).toBeTruthy();
    expect(result10).toStrictEqual(123);
    expect(result11).toStrictEqual(123);
  });
});
