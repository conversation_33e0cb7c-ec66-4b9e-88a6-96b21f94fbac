import { FvDate } from '@/domain/value-objects/Date';

describe(`${FvDate.name}`, () => {
  // https://en.wikipedia.org/wiki/List_of_tz_database_time_zones

  it('should return date', () => {
    const date1 = FvDate.createInitialEpoch();
    const date2 = new Date(0);

    expect(date1.getSeconds()).toStrictEqual(date2.getSeconds());
  });

  it('should return timezoned date', () => {
    const date1 = FvDate.createInitialEpoch().addHours(8);
    const date2 = FvDate.createInitialEpoch().addHours(8);

    const formattedOffset2 = date1.toFormat({ timeZone: 'Libya' }); // +2, so 8:00 --> 10:00
    const formattedManualOffset2 = date1.addHours(2).toFormat({ timeZone: 'Africa/Accra' }); // +0, but manual offset is applied

    const formattedOffset1 = date2.toFormat({ timeZone: 'Africa/Tunis' }); // +1, so  8:00 --> 9:00

    expect(formattedOffset2).not.toStrictEqual(formattedOffset1);
    expect(formattedOffset2).toStrictEqual(formattedManualOffset2);
  });

  it('should ignore undefined and empty timezones, and set default timezone', () => {
    const date1 = FvDate.createInitialEpoch();

    const formatted1 = date1.toFormat({ timeZone: undefined });
    const formatted2 = date1.toFormat({ timeZone: '' });
    const formatted3 = date1.toFormat({ timeZone: 'Europe/Madrid' });

    expect(formatted1).toStrictEqual(formatted2);
    expect(formatted2).toStrictEqual(formatted3);
  });
});
