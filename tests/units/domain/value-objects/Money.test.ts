import { ECurrency } from '@/domain/enums/Currencies';
import { Money } from '@/domain/value-objects/Money';
import { MotherCreator } from '@tests/__stub__/MotherCreator';

const ITERATIONS = 100;

describe(`${Money.name}`, () => {
  const createRandomFloat = (fractionDigits = 2): number => {
    return MotherCreator.number().float({
      min: 0, max: 100, fractionDigits,
    });
  };

  function roundHalfEvenWithTolerance(value: number, fractionDigits = 2): number {
    // Epsilon: una pequeña tolerancia para comparar con 0.5
    // Debe ser lo suficientemente pequeño para no clasificar erróneamente
    // fracciones que genuinamente no son 0.5, pero lo suficientemente grande
    // para capturar el ruido de punto flotante alrededor de 0.5.
    // 1e-9 suele ser un buen punto de partida para números de doble precisión.
    const epsilon = 1e-9;
    const factor = Math.pow(10, fractionDigits);

    // 1. Escalar el número.
    // Aquí es donde pueden ocurrir las imprecisiones iniciales si 'value' ya tiene ruido
    // o si la multiplicación introduce más.
    // Ejemplo: 26.75 * 89.34 = 2389.845 (matemáticamente)
    // valueAsNumber = 2389.8450000000003 (posible valor en JS)
    // scaledValue = 238984.50000000003
    //
    // Ejemplo: 1.845
    // valueAsNumber = 1.845
    // scaledValue = 184.5 (a veces puede ser 184.49999999999997)
    const scaledValue = value * factor;

    // 2. Obtener la parte entera y la parte fraccional del valor escalado.
    const floorPart = Math.floor(scaledValue);
    const fractionPart = scaledValue - floorPart;
    // Para scaledValue = 238984.50000000003:
    // floorPart = 238984
    // fractionPart = 0.50000000003
    //
    // Para scaledValue = 184.49999999999997:
    // floorPart = 184
    // fractionPart = 0.49999999999997

    let resultBase: number;

    // 3. Comparar la parte fraccional con 0.5 usando la tolerancia.
    if (Math.abs(fractionPart - 0.5) < epsilon) {
      if (floorPart % 2 === 0) {
        resultBase = floorPart;
      } else {
        resultBase = floorPart + 1;
      }
    } else if (fractionPart < 0.5) {
      resultBase = floorPart;
    } else {
      resultBase = floorPart + 1;
    }

    // 4. Desescalar el resultado.
    return resultBase / factor;
  }

  const buildMoney = (amount: number, currency: ECurrency): Money => Money.build({ amount, currency }).value as Money;

  const currency = ECurrency.EUR;

  it('should build correctly', () => {
    for (let i = 0; i < ITERATIONS; i++) {
      const amount = createRandomFloat();
      const money = buildMoney(amount, currency);

      expect(money.toDecimal()).toStrictEqual(amount);
      expect(money.currency).toStrictEqual(currency);

      const fractionDigits = i % 10;
      const amountWithMoreFractionDigits = createRandomFloat(fractionDigits);
      const moneyRounded = buildMoney(amountWithMoreFractionDigits, currency);

      const expectedAmount = roundHalfEvenWithTolerance(amountWithMoreFractionDigits);

      expect(moneyRounded.toDecimal()).toStrictEqual(expectedAmount);
      expect(moneyRounded.currency).toStrictEqual(currency);
    }
  });

  it('should return the sum of two numbers', () => {
    const amount = createRandomFloat();
    const money = buildMoney(amount, currency);

    const amountToAdd = createRandomFloat();
    const moneyToAdd = buildMoney(amountToAdd, currency);

    const finalMoney = money.add(moneyToAdd);

    const partialResult = amount + amountToAdd;
    const expectedResult = roundHalfEvenWithTolerance(partialResult);

    expect(finalMoney.toDecimal()).toStrictEqual(expectedResult);
    expect(finalMoney.currency).toStrictEqual(currency);
  });

  it('should return the difference between two numbers', () => {
    const amount = createRandomFloat();
    const money = buildMoney(amount, currency);

    const amountToSubtract = createRandomFloat();
    const moneyToSubtract = buildMoney(amountToSubtract, currency);

    const finalMoney = money.subtract(moneyToSubtract);

    const partialResult = amount - amountToSubtract;
    const expectedResult = roundHalfEvenWithTolerance(partialResult);

    expect(finalMoney.toDecimal()).toStrictEqual(expectedResult);
    expect(finalMoney.currency).toStrictEqual(currency);
  });

  it('should return the percentage calculation', () => {
    for (let i = 0; i < ITERATIONS; i++) {
      const amount = createRandomFloat();
      const money = buildMoney(amount, currency);

      const percentage = createRandomFloat();
      const partialResult = amount * (percentage / 100);
      const expectedResult = roundHalfEvenWithTolerance(partialResult);

      const finalMoney = money.percentage(percentage);

      expect(finalMoney.toDecimal()).toStrictEqual(expectedResult);
      expect(finalMoney.currency).toStrictEqual(currency);
    }
  });

  it('should return the addPercentage calculation', () => {
    for (let i = 0; i < ITERATIONS; i++) {
      const amount = createRandomFloat();
      const money = buildMoney(amount, currency);

      const percentage = createRandomFloat();
      const totalResult = (amount * (percentage / 100)) + amount;
      const expectedResult = roundHalfEvenWithTolerance(totalResult);

      const finalMoney = money.addPercentage(percentage);

      expect(finalMoney.toDecimal()).toStrictEqual(expectedResult);
      expect(finalMoney.currency).toStrictEqual(currency);
    }
  });

  it('should return the subtractPercentage calculation', () => {
    for (let i = 0; i < ITERATIONS; i++) {
      const amount = createRandomFloat();
      const money = buildMoney(amount, currency);

      const percentage = createRandomFloat();
      const totalResult = amount - (amount * (percentage / 100));
      const expectedResult = roundHalfEvenWithTolerance(totalResult);

      const finalMoney = money.subtractPercentage(percentage);


      expect(finalMoney.toDecimal()).toStrictEqual(expectedResult);
      expect(finalMoney.currency).toStrictEqual(currency);
    }
  });

  it('should return the amount divided into parts', () => {
    for (let i = 0; i < ITERATIONS; i++) {
      const amount = createRandomFloat();
      const money = buildMoney(amount, currency);

      let quantity = createRandomFloat();

      while (quantity === 0) {
        quantity = createRandomFloat();
      }

      const partialResult = amount / quantity;
      const expectedResult = roundHalfEvenWithTolerance(partialResult);

      const finalMoney = money.divided(quantity);

      expect(finalMoney.toDecimal()).toStrictEqual(expectedResult);
      expect(finalMoney.currency).toStrictEqual(currency);
    }
  });

  it('should return the multiply of two numbers', () => {
    for (let i = 0; i < ITERATIONS; i++) {
      const amount = createRandomFloat();
      const factor = createRandomFloat();

      const partialResult = amount * factor;
      const expectedResult = roundHalfEvenWithTolerance(partialResult);

      const money = buildMoney(amount, currency);
      const finalMoney = money.multiply(factor);

      expect(finalMoney.toDecimal()).toStrictEqual(expectedResult);
      expect(finalMoney.currency).toStrictEqual(currency);
    }
  });

  it('should return that a value is positive', () => {
    const amount = createRandomFloat();
    const money = buildMoney(amount, currency);

    expect(money.isPositive()).toBeTruthy();
  });

  it('should return that a value is negative', () => {
    const money1 = buildMoney(5, currency);
    const money2 = buildMoney(10, currency);
    const newMoney = money1.subtract(money2);

    expect(newMoney.isNegative()).toBeTruthy();
  });

  it('should return that a value is zero', () => {
    const zero = 0;
    const money = buildMoney(zero, currency);

    expect(money.isZero()).toBeTruthy();
    expect(money.toDecimal()).toStrictEqual(zero);

    const money2 = Money.buildZero(currency);

    expect(money2.isZero()).toBeTruthy();
    expect(money2.toDecimal()).toStrictEqual(zero);
  });

  it('should return that two values ​​are GTE or GT', () => {
    const amount1 = 2;
    const money1 = buildMoney(amount1, currency);

    const amount2 = 1;
    const money2 = buildMoney(amount2, currency);

    const money3 = buildMoney(amount1, currency);

    expect(money1.gte(money2)).toBeTruthy();
    expect(money1.gte(money3)).toBeTruthy();
    expect(money1.gt(money2)).toBeTruthy();
  });

  it('should return that two values ​are LTE or LT', () => {
    const amount1 = 1;
    const money1 = buildMoney(amount1, currency);

    const amount2 = 2;
    const money2 = buildMoney(amount2, currency);

    const money3 = buildMoney(amount1, currency);

    expect(money1.lte(money2)).toBeTruthy();
    expect(money1.lte(money3)).toBeTruthy();
    expect(money1.lt(money2)).toBeTruthy();
  });

  it('should return that two values ​​are EQ', () => {
    const amount = 1.05;
    const money1 = buildMoney(amount, currency);
    const money2 = buildMoney(amount, currency);

    expect(money1.eq(money2)).toBeTruthy();
  });

  it('check particular case: ticket business logic with custom fees', () => {
    const ticketsQuantity = 3;
    const ticketUnitPrice = 10;
    const adminFeesPercentage = 8;
    const customFeePercentage = 21;

    const ticketCustomFees = ticketUnitPrice * (customFeePercentage / 100);
    const ticketCustomFeesRounded = roundHalfEvenWithTolerance(ticketCustomFees);
    const ticketWithCustomFees = roundHalfEvenWithTolerance(ticketUnitPrice + ticketCustomFeesRounded);

    const ticketAdminFees = ticketWithCustomFees * (adminFeesPercentage / 100);
    const ticketAdminFeesRounded = roundHalfEvenWithTolerance(ticketAdminFees);

    const ticketUnitTotal = roundHalfEvenWithTolerance(ticketWithCustomFees + ticketAdminFeesRounded);
    const totalImport = ticketsQuantity * ticketUnitTotal;
    const totalImportRounded = roundHalfEvenWithTolerance(totalImport);

    const moneyTicketPrice = buildMoney(ticketUnitPrice, currency);
    const moneyCustomFees = moneyTicketPrice.percentage(customFeePercentage);
    const moneyWithCustomFees = moneyTicketPrice.add(moneyCustomFees);
    const moneyAdminFees = moneyWithCustomFees.percentage(adminFeesPercentage);
    const moneyUnitTotal = moneyWithCustomFees.add(moneyAdminFees);
    const moneyTotal = moneyUnitTotal.multiply(ticketsQuantity);

    expect(moneyCustomFees.toDecimal()).toBe(ticketCustomFeesRounded);
    expect(moneyWithCustomFees.toDecimal()).toBe(ticketWithCustomFees);
    expect(moneyAdminFees.toDecimal()).toBe(ticketAdminFeesRounded);
    expect(moneyUnitTotal.toDecimal()).toBe(ticketUnitTotal);
    expect(totalImportRounded).toBe(moneyTotal.toDecimal());
  });

  it('check particular case: ticket business logic with % discount', () => {
    const ticketsQuantity = 10;
    const ticketUnitPrice = 10.55;
    const adminFeesPercentage = 8;
    const discountPercentage = 10;

    const sumTicketPrices = ticketUnitPrice * ticketsQuantity;
    const sumTicketPricesRounded = roundHalfEvenWithTolerance(sumTicketPrices);
    const totalDiscount = sumTicketPricesRounded * (discountPercentage / 100);
    const totalDiscountRounded = roundHalfEvenWithTolerance(totalDiscount);
    const ticketDiscount = totalDiscountRounded / ticketsQuantity;
    const ticketDiscountRounded = roundHalfEvenWithTolerance(ticketDiscount);
    const ticketWithDiscount = ticketUnitPrice - ticketDiscountRounded;
    const ticketWithDiscountRounded = roundHalfEvenWithTolerance(ticketWithDiscount);

    const ticketAdminFees = ticketWithDiscountRounded * (adminFeesPercentage / 100);
    const ticketAdminFeesRounded = roundHalfEvenWithTolerance(ticketAdminFees);

    const ticketUnitTotal = roundHalfEvenWithTolerance(ticketWithDiscountRounded + ticketAdminFeesRounded);
    const totalImport = ticketsQuantity * ticketUnitTotal;
    const totalImportRounded = roundHalfEvenWithTolerance(totalImport);

    const moneyTicketPrice = buildMoney(ticketUnitPrice, currency);
    const moneySumPrices = moneyTicketPrice.multiply(ticketsQuantity);
    const moneyTotalDiscount = moneySumPrices.percentage(discountPercentage);
    const moneyDiscountPerTicket = moneyTotalDiscount.divided(ticketsQuantity);
    const moneyWithDiscount = moneyTicketPrice.subtract(moneyDiscountPerTicket);
    const moneyAdminFees = moneyWithDiscount.percentage(adminFeesPercentage);
    const moneyUnitTotal = moneyWithDiscount.add(moneyAdminFees);
    const moneyTotal = moneyUnitTotal.multiply(ticketsQuantity);

    expect(moneySumPrices.toDecimal()).toBe(sumTicketPricesRounded);
    expect(moneyTotalDiscount.toDecimal()).toBe(totalDiscountRounded);
    expect(moneyDiscountPerTicket.toDecimal()).toBe(ticketDiscountRounded);
    expect(moneyWithDiscount.toDecimal()).toBe(ticketWithDiscountRounded);
    expect(moneyAdminFees.toDecimal()).toBe(ticketAdminFeesRounded);
    expect(moneyUnitTotal.toDecimal()).toBe(ticketUnitTotal);
    expect(totalImportRounded).toBe(moneyTotal.toDecimal());
  });
});
