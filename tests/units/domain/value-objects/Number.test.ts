import { FvNumber } from '@/domain/value-objects/Number';

describe(`${FvNumber.name}`, () => {
  it('should return is a number', () => {
    expect(FvNumber.is(0)).toBeTruthy();
    expect(FvNumber.is(1)).toBeTruthy();
    expect(FvNumber.is(-1)).toBeTruthy();
    expect(FvNumber.is(0.1)).toBeTruthy();
    expect(FvNumber.is(-0.1)).toBeTruthy();
  });

  it('should return is not a number', () => {
    expect(FvNumber.is(NaN)).toBeFalsy();
    expect(FvNumber.is('string')).toBeFalsy();
    expect(FvNumber.is(null)).toBeFalsy();
    expect(FvNumber.is(true)).toBeFalsy();
    expect(FvNumber.is(undefined)).toBeFalsy();
  });
});
