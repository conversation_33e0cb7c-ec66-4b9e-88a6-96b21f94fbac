import { FvDate } from '@/domain/value-objects/Date';
import { UpdatedAt } from '@/domain/value-objects/UpdatedAt';

describe(`${UpdatedAt.name}`, () => {
  const date = FvDate.create();

  it('should return date primitive', () => {
    const updatedAtByDate = UpdatedAt.build(date.toPrimitive());

    expect(date.toPrimitive()).toStrictEqual(updatedAtByDate.toPrimitive());
  });

  it('should return iso  date', () => {
    const isoDate = date.toISO();
    const updatedAtByISO = UpdatedAt.build(isoDate);

    expect(date.toPrimitive()).toStrictEqual(updatedAtByISO.toPrimitive());
  });

  it('should return date in milliseconds', () => {
    const millisecondDate = date.toMilliseconds();
    const updatedAtByMs = UpdatedAt.build(millisecondDate);

    expect(date.toPrimitive()).toStrictEqual(updatedAtByMs.toPrimitive());
  });

  it('should return the current date when the iso date is incorrect', () => {
    const badIsoDate = '2025-08-05L07:45:01.620Z';
    const updatedAtByMs = UpdatedAt.build(badIsoDate);

    expect(date.toMilliseconds()).toBeLessThan(updatedAtByMs.toMilliseconds());
  });
});
