import { Collection } from '@/domain/collections/Collection';
import { BinarySearch } from '@/domain/services/BinarySearch';
import { FvNumber } from '@/domain/value-objects/Number';
import { FvString } from '@/domain/value-objects/String';

type Target = string | number | ObjectItem;

type ObjectItem = {
  readonly id: number;
  readonly name: string;
};

type Results = {
  readonly 'numberOfRecord': number | string;
  readonly 'target': Target;
  readonly 'binarySearch': number;
  readonly 'collection->getByIndex': number;
  readonly 'collection->getByValue': number;
  readonly 'array->findIndex': number;
  readonly 'array->find': number;
};

describe(`BinarySearch`, () => {
  const length = 1_000_000;

  const finalResults: Array<Results> = [];
  const numberCollection = Collection.new<number>();
  const stringCollection = Collection.new<string>();
  const objectCollection = Collection.new<ObjectItem>('id');

  beforeAll(async () => {
    Array.from({ length }, (_, i) => {
      numberCollection.add(i);
      stringCollection.add(`Name-${i}`);
      objectCollection.add({ id: i, name: `Name-${i}` });
    });

    numberCollection.sort((a, b) => FvNumber.build(a).compareTo(b));
    stringCollection.sort((a, b) => FvString.build(a).compareTo(b));
  });

  describe(`number list`, () => {
    it('should more efficient that find and findIndex', () => {
      const numberList = numberCollection.toArray();
      const target = length - 1;

      const startCollectionByIndex = performance.now();
      const valueCollectionByIndex = numberCollection.getByIndex(target).get();
      const endCollectionByIndex = performance.now();

      const startCollectionByValue = performance.now();
      const valueCollectionByValue = numberCollection.getByValue(target).get();
      const endCollectionByValue = performance.now();

      const startBinary = performance.now();
      const foundBinary = BinarySearch.search(numberList, target);
      const valueBinary = numberList.at(foundBinary);
      const endBinary = performance.now();

      const startFindIndex = performance.now();
      const indexFindIndex = numberList.findIndex(value => value === target);
      const valueFindIndex = numberList.at(indexFindIndex);
      const endFindIndex = performance.now();

      const startFind = performance.now();
      const valueFind = numberList.find(value => value === target);
      const endFind = performance.now();

      const binarySearchTime = endBinary - startBinary;
      const collectionSearchByIndexTime = endCollectionByIndex - startCollectionByIndex;
      const collectionSearchByValueTime = endCollectionByValue - startCollectionByValue;
      const findIndexSearchTime = endFindIndex - startFindIndex;
      const findSearchTime = endFind - startFind;

      expect(valueBinary).toBe(target);
      expect(valueCollectionByIndex).toBe(target);
      expect(valueCollectionByValue).toBe(target);
      expect(valueFindIndex).toBe(target);
      expect(valueFind).toBe(target);

      expect(binarySearchTime).toBeLessThan(findIndexSearchTime);
      expect(binarySearchTime).toBeLessThan(findSearchTime);

      expect(collectionSearchByIndexTime).toBeLessThan(findIndexSearchTime);
      expect(collectionSearchByValueTime).toBeLessThan(findSearchTime);

      finalResults.push({
        'numberOfRecord': FvNumber.build(numberCollection.length()).format(),
        target,
        'binarySearch': binarySearchTime,
        'collection->getByIndex': collectionSearchByIndexTime,
        'collection->getByValue': collectionSearchByValueTime,
        'array->findIndex': findIndexSearchTime,
        'array->find': findSearchTime,
      });
    });
  });

  describe(`string list`, () => {
    it('should more efficient that find and findIndex', () => {
      const stringList = stringCollection.toArray();
      const target = stringList.at(-1) as string;

      const startCollectionByIndex = performance.now();
      const valueCollectionByIndex = stringCollection.getByIndex(target).get();
      const endCollectionByIndex = performance.now();

      const startCollectionByValue = performance.now();
      const valueCollectionByValue = stringCollection.getByValue(target).get();
      const endCollectionByValue = performance.now();

      const startBinary = performance.now();
      const foundBinary = BinarySearch.search(stringList, target);
      const valueBinary = stringList.at(foundBinary);
      const endBinary = performance.now();

      const startFindIndex = performance.now();
      const indexFindIndex = stringList.findIndex(value => value === target);
      const valueFindIndex = stringList.at(indexFindIndex);
      const endFindIndex = performance.now();

      const startFind = performance.now();
      const valueFind = stringList.find(value => value === target);
      const endFind = performance.now();

      const binarySearchTime = endBinary - startBinary;
      const collectionSearchByIndexTime = endCollectionByIndex - startCollectionByIndex;
      const collectionSearchByValueTime = endCollectionByValue - startCollectionByValue;
      const findIndexSearchTime = endFindIndex - startFindIndex;
      const findSearchTime = endFind - startFind;

      expect(valueBinary).toBe(target);
      expect(valueCollectionByIndex).toBe(target);
      expect(valueCollectionByValue).toBe(target);
      expect(valueFindIndex).toBe(target);
      expect(valueFind).toBe(target);

      expect(binarySearchTime).toBeLessThan(findIndexSearchTime);
      expect(binarySearchTime).toBeLessThan(findSearchTime);

      expect(collectionSearchByIndexTime).toBeLessThan(findIndexSearchTime);
      expect(collectionSearchByValueTime).toBeLessThan(findSearchTime);

      finalResults.push({
        'numberOfRecord': FvNumber.build(stringCollection.length()).format(),
        target,
        'binarySearch': binarySearchTime,
        'collection->getByIndex': collectionSearchByIndexTime,
        'collection->getByValue': collectionSearchByValueTime,
        'array->findIndex': findIndexSearchTime,
        'array->find': findSearchTime,
      });
    });
  });

  describe(`object list`, () => {
    it('should more efficient that find and findIndex', () => {
      const objectList = objectCollection.toArray();
      const target = objectList.at(-1) as ObjectItem;

      const startCollectionByIndex = performance.now();
      const valueCollectionByIndex = objectCollection.getByIndex(target.id).get();
      const endCollectionByIndex = performance.now();

      const startCollectionByValue = performance.now();
      const valueCollectionByValue = objectCollection.getByValue(target).get();
      const endCollectionByValue = performance.now();

      const startBinary = performance.now();
      const foundBinary = BinarySearch.search(objectList, target, (a, b) => a.id - b.id);
      const valueBinary = objectList.at(foundBinary) as ObjectItem;
      const endBinary = performance.now();

      const startFindIndex = performance.now();
      const indexFindIndex = objectList.findIndex(value => value.id === target.id);
      const valueFindIndex = objectList.at(indexFindIndex) as ObjectItem;
      const endFindIndex = performance.now();

      const startFind = performance.now();
      const valueFind = objectList.find(value => value.id === target.id) as ObjectItem;
      const endFind = performance.now();

      const binarySearchTime = endBinary - startBinary;
      const collectionSearchByIndexTime = endCollectionByIndex - startCollectionByIndex;
      const collectionSearchByValueTime = endCollectionByValue - startCollectionByValue;
      const findIndexSearchTime = endFindIndex - startFindIndex;
      const findSearchTime = endFind - startFind;

      expect(valueBinary.id).toBe(target.id);
      expect(valueBinary.name).toBe(target.name);

      expect(valueCollectionByIndex.id).toBe(target.id);
      expect(valueCollectionByIndex.name).toBe(target.name);

      expect(valueCollectionByValue.id).toBe(target.id);
      expect(valueCollectionByValue.name).toBe(target.name);

      expect(valueFindIndex.id).toBe(target.id);
      expect(valueFindIndex.name).toBe(target.name);

      expect(valueFind.id).toBe(target.id);
      expect(valueFind.name).toBe(target.name);

      expect(binarySearchTime).toBeLessThan(findIndexSearchTime);
      expect(binarySearchTime).toBeLessThan(findSearchTime);

      expect(collectionSearchByIndexTime).toBeLessThan(findIndexSearchTime);
      expect(collectionSearchByValueTime).toBeLessThan(findSearchTime);

      finalResults.push({
        'numberOfRecord': FvNumber.build(objectCollection.length()).format(),
        target,
        'binarySearch': binarySearchTime,
        'collection->getByIndex': collectionSearchByIndexTime,
        'collection->getByValue': collectionSearchByValueTime,
        'array->findIndex': findIndexSearchTime,
        'array->find': findSearchTime,
      });
    });
  });

  afterAll(() => {
    // eslint-disable-next-line no-console
    console.table(finalResults);
  });
});
