import { convertToJson, Maybe } from '@/domain';
import { Collection } from '@/domain/collections/Collection';

describe(`${convertToJson.name}`, () => {
  class TestEntity<T> {
    value: T;

    constructor(value: T) {
      this.value = value;
    }

    toPrimitives(): T {
      return this.value;
    }
  }

  it('should convert collection to json', () => {
    const entity = new TestEntity(Collection.build([
      { id: 1, name: 'test1' },
      { id: 2, name: 'test2' },
    ], 'id'));
    const objectJson = convertToJson(entity);

    expect(entity.value.toArray()).toStrictEqual(objectJson);
  });

  it('should convert maybe to json', () => {
    const entity = new TestEntity(Maybe.some('test'));
    const objectJson = convertToJson(entity);

    expect(entity.value.get()).toStrictEqual(objectJson);
  });

  it('should convert set to json', () => {
    const entity = new TestEntity(new Set(['test1', 'test2']));
    const objectJson = convertToJson(entity);

    expect(Array.from(entity.value)).toStrictEqual(objectJson);
  });

  it('should convert map to json', () => {
    const entity = new TestEntity(new Map([['key1', 'value1'], ['key2', 'value2']]));
    const objectJson = convertToJson(entity);

    expect(Array.from(entity.value.entries())).toStrictEqual(objectJson);
  });

  it('should convert array to json', () => {
    const entity = new TestEntity(['test1', 'test2']);
    const objectJson = convertToJson(entity);

    expect(entity.value).toStrictEqual(objectJson);
  });

  it('should convert object to json', () => {
    const entity = new TestEntity({ key1: 'value1', key2: 'value2' });
    const objectJson = convertToJson(entity);

    expect(entity.value).toStrictEqual(objectJson);
  });

  it('should convert nested objects to json', () => {
    const entity = new TestEntity({
      key1: 'value1',
      key2: {
        key3: 'value3',
        key4: 'value4',
      },
    });
    const objectJson = convertToJson(entity);

    expect(entity.value).toStrictEqual(objectJson);
  });

  it('should convert date to json', () => {
    const entity = new TestEntity(new Date());
    const objectJson = convertToJson(entity);

    expect(entity.value.toISOString()).toStrictEqual(objectJson);
  });

  it('should convert number to json', () => {
    const entity = new TestEntity(1);
    const objectJson = convertToJson(entity);

    expect(entity.value).toStrictEqual(objectJson);
  });

  it('should convert string to json', () => {
    const entity = new TestEntity('test');
    const objectJson = convertToJson(entity);

    expect(entity.value).toStrictEqual(objectJson);
  });

  it('should convert empty string to json', () => {
    const entity = new TestEntity('');
    const objectJson = convertToJson(entity);

    expect(entity.value).toStrictEqual(objectJson);
  });

  it('should convert boolean to json', () => {
    const entity = new TestEntity(true);
    const objectJson = convertToJson(entity);

    expect(entity.value).toStrictEqual(objectJson);
  });

  it('should convert null to json', () => {
    const entity = new TestEntity(null);
    const objectJson = convertToJson(entity);

    expect(entity.value).toStrictEqual(objectJson);
  });

  it('should convert undefined to json', () => {
    const entity = new TestEntity(undefined);
    const objectJson = convertToJson(entity);

    expect(entity.value).toStrictEqual(objectJson);
  });
});
