import { Collection } from '@/domain/collections/Collection';

describe(`${Collection.name}`, () => {
  it('should return an existing first item, when collection is built with objects', () => {
    const collection = Collection.build([{ id: 1 }, { id: 2 }, { id: 3 }], 'id');

    expect(collection.first().fold(() => null, value => value.id)).toStrictEqual(1);
  });

  it('should return an existing first item, when collection is built with numbers', () => {
    const collection = Collection.build([1, 2, 3]);

    expect(collection.first().fold(() => null, value => value)).toStrictEqual(1);
  });

  it('should return an existing first item, when collection is built with strings', () => {
    const collection = Collection.build(['a', 'b', 'c']);

    expect(collection.first().fold(() => null, value => value)).toStrictEqual('a');
  });

  it('should return an empty first item', () => {
    const collection = Collection.new<{ id: number; }>('id');

    expect(collection.first().fold(() => null, value => value.id)).toBeNull();
  });

  it('should return first item for object array', () => {
    const dataForCollection = [
      { id: 1, name: 'emi1' },
      { id: 2, name: 'emi2' },
      { id: 3, name: 'emi3' },
      { id: 4, name: 'emi4' },
      { id: 5, name: 'emi5' },
    ];
    const collection = Collection.build<{ id: number; }>(dataForCollection, 'id');

    expect(collection.first().fold(() => null, value => value.id)).toStrictEqual(1);
  });

  it('should return an existing last item, when collection is built with objects', () => {
    const collection = Collection.build([{ id: 1 }, { id: 2 }, { id: 3 }], 'id');

    expect(collection.last().fold(() => null, value => value.id)).toStrictEqual(3);
  });

  it('should return an existing last item, when collection is built with numbers', () => {
    const collection = Collection.build([1, 2, 3]);

    expect(collection.last().fold(() => null, value => value)).toStrictEqual(3);
  });

  it('should return an existing last item, when collection is built with strings', () => {
    const collection = Collection.build(['a', 'b', 'c']);

    expect(collection.last().fold(() => null, value => value)).toStrictEqual('c');
  });

  it('should return an empty last item', () => {
    const collection = Collection.new<{ id: number; }>('id');

    expect(collection.last().fold(() => null, value => value.id)).toBeNull();
  });

  it('should return last item for object array', () => {
    const dataForCollection = [
      { id: 1, name: 'emi1' },
      { id: 2, name: 'emi2' },
      { id: 3, name: 'emi3' },
      { id: 4, name: 'emi4' },
      { id: 5, name: 'emi5' },
    ];
    const collection = Collection.build<{ id: number; }>(dataForCollection, 'id');

    expect(collection.last().fold(() => null, value => value.id)).toStrictEqual(5);
  });
});
