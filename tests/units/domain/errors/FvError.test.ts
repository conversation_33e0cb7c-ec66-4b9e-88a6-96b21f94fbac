import { ECurrency } from '@/domain/enums/Currencies';
import { FvError } from '@/domain/errors/FvError';
import { ImageError } from '@/domain/errors/ImageError';
import { InvalidArgumentError } from '@/domain/errors/InvalidArgumentError';
import { InvalidDateError } from '@/domain/errors/InvalidDateError';
import { NotFoundError } from '@/domain/errors/NotFoundError';
import { UnexpectedError } from '@/domain/errors/UnexpectedError';
import { Money } from '@/domain/value-objects/Money';

import type { FvErrorContextualizeDto, FvErrorStack } from '@/domain/errors/FvError';
import type { MoneyError } from '@/domain/errors/MoneyError';

describe(`${FvError.name}`, () => {
  const errorDto1: FvErrorContextualizeDto = {
    context: 'context_1',
    data: { id: '1111' },
  };

  const errorDto2: FvErrorContextualizeDto = {
    context: 'context_2',
    data: { id: '2222' },
  };

  const errorDto3: FvErrorContextualizeDto = {
    context: 'context_3',
    data: { id: '3333' },
  };

  const errorDto4: FvErrorContextualizeDto = {
    context: 'context_4',
    data: { id: '4444' },
  };

  it('should return error', () => {
    const baseError = NotFoundError.build({
      context: errorDto1.context, target: errorDto1.context, data: errorDto1.data,
    });

    expect(baseError).toBeInstanceOf(NotFoundError);
    expect(baseError.context).toEqual(errorDto1.context);
    expect(baseError.message).toEqual(`${errorDto1.context} not found`);
    expect(baseError.cause).toEqual('entity_not_found');
    expect(baseError.data).toStrictEqual(errorDto1.data);
    expect(baseError.getStack()).toHaveLength(1);
  });

  it('should output structured log of error with 1 error stack', () => {
    const errorWithContext2 = NotFoundError.build({
      context: errorDto2.context, target: errorDto2.context, data: errorDto2.data,
    });

    const structuredLog = errorWithContext2.getStack();

    expect(structuredLog.length).toEqual(1);

    const currentIndex = 0;

    expect(structuredLog[currentIndex]?.message).toEqual(`${errorDto2.context} not found`);
    expect(structuredLog[currentIndex]?.context).toEqual(errorDto2.context);
    expect(structuredLog[currentIndex]?.data).toStrictEqual(errorDto2.data);
    expect(structuredLog[currentIndex]?.step).toEqual(currentIndex + 1);
  });

  it('should output structured log of error with 2 error stack, contextualize without data', () => {
    const errorWithContext2 = NotFoundError
      .build({ context: errorDto2.context, target: errorDto2.context })
      .contextualize({ context: errorDto3.context });

    const structuredLog = errorWithContext2.getStack();

    expect(structuredLog.length).toEqual(2);

    const currentIndex = 1;

    expect(structuredLog[currentIndex]?.message).toEqual(`${errorDto2.context} not found`);
    expect(structuredLog[currentIndex]?.context).toEqual(errorDto2.context);
    expect(structuredLog[currentIndex]?.data).toBeUndefined();
    expect(structuredLog[currentIndex]?.step).toEqual(currentIndex + 1);
  });

  it('should output structured log of error with 4 error stacks', () => {
    const errorWithContext4 = NotFoundError.build({
      context: errorDto1.context,
      data: errorDto1.data,
    })
      .contextualize(errorDto2)
      .contextualize(errorDto3)
      .contextualize(errorDto4);

    const structuredLog = errorWithContext4.getStack();

    expect(structuredLog.length).toEqual(4);

    let currentIndex = 0;

    expect(structuredLog[currentIndex]?.message).toBeUndefined();
    expect(structuredLog[currentIndex]?.context).toEqual(errorDto4.context);
    expect(structuredLog[currentIndex]?.data).toStrictEqual(errorDto4.data);
    expect(structuredLog[currentIndex]?.step).toEqual(currentIndex + 1);

    currentIndex++;

    expect(structuredLog[currentIndex]?.message).toBeUndefined();
    expect(structuredLog[currentIndex]?.context).toEqual(errorDto3.context);
    expect(structuredLog[currentIndex]?.data).toStrictEqual(errorDto3.data);
    expect(structuredLog[currentIndex]?.step).toEqual(currentIndex + 1);

    currentIndex++;

    expect(structuredLog[currentIndex]?.message).toBeUndefined();
    expect(structuredLog[currentIndex]?.context).toEqual(errorDto2.context);
    expect(structuredLog[currentIndex]?.data).toEqual(errorDto2.data);
    expect(structuredLog[currentIndex]?.step).toEqual(currentIndex + 1);

    currentIndex++;

    expect(structuredLog[currentIndex]?.message).toEqual('Entity not found');
    expect(structuredLog[currentIndex]?.context).toEqual(errorDto1.context);
    expect(structuredLog[currentIndex]?.data).toStrictEqual(errorDto1.data);
    expect(structuredLog[currentIndex]?.step).toEqual(currentIndex + 1);
  });

  it('should try-catch UnexpectedError error', () => {
    try {
      throw UnexpectedError.build({ ...errorDto1 });
    } catch (error) {
      const parsedError = error as UnexpectedError;

      expect(parsedError).toBeInstanceOf(UnexpectedError);
      expect(parsedError.context).toEqual(errorDto1.context);
      expect(parsedError.message).toEqual('Unexpected error');
      expect(parsedError.cause).toEqual('unexpected_error');
      expect(parsedError.data).toStrictEqual(errorDto1.data);
    }
  });

  it('should return correct error for all InvalidArgumentError methods', () => {
    const invalidArgError1 = InvalidArgumentError.build({
      context: errorDto1.context,
      data: errorDto1.data,
    });

    expect(invalidArgError1).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError1.context).toEqual(errorDto1.context);
    expect(invalidArgError1.cause).toEqual(InvalidArgumentError.defaultCause);
    expect(invalidArgError1.data).toStrictEqual(errorDto1.data);

    const filterValue = 'invalid_operator';
    const invalidArgError2 = InvalidArgumentError.filterOperator({ context: errorDto2.context, target: filterValue });

    expect(invalidArgError2).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError2.context).toEqual(errorDto2.context);
    expect(invalidArgError2.message).toEqual(`The filter operator ${filterValue} is invalid`);
    expect(invalidArgError2.cause).toEqual(InvalidArgumentError.defaultCause);

    const currency = 'XYZ';
    const invalidArgError3 = InvalidArgumentError.currencyNotSupported({ context: errorDto2.context, target: currency });

    expect(invalidArgError3).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError3.context).toEqual(errorDto2.context);
    expect(invalidArgError3.message).toEqual(`Currency ${currency} not supported`);
    expect(invalidArgError3.cause).toEqual(InvalidArgumentError.defaultCause);

    const invalidArgError4 = InvalidArgumentError.amountCannotBeNegative({ context: errorDto2.context, target: -1 });

    expect(invalidArgError4).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError4.context).toEqual(errorDto2.context);
    expect(invalidArgError4.message).toEqual('Amount cannot be negative.');
    expect(invalidArgError4.cause).toEqual(InvalidArgumentError.defaultCause);

    const docType = 'INVALID_DOC';
    const invalidArgError5 = InvalidArgumentError.invalidDocumentType({ context: errorDto2.context, target: docType });

    expect(invalidArgError5).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError5.context).toEqual(errorDto2.context);
    expect(invalidArgError5.message).toEqual(`The document type ${docType} is invalid`);
    expect(invalidArgError5.cause).toEqual(InvalidArgumentError.defaultCause);

    const docNumber = '12345X';
    const invalidArgError6 = InvalidArgumentError.invalidDocumentNumber({ context: errorDto2.context, target: docNumber });

    expect(invalidArgError6).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError6.context).toEqual(errorDto2.context);
    expect(invalidArgError6.message).toEqual(`The document number ${docNumber} is invalid`);
    expect(invalidArgError6.cause).toEqual(InvalidArgumentError.defaultCause);

    const phone = '+1234';
    const invalidArgError7 = InvalidArgumentError.invalidPhone({ context: errorDto2.context, target: phone });

    expect(invalidArgError7).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError7.context).toEqual(errorDto2.context);
    expect(invalidArgError7.message).toEqual(`Invalid phone: ${phone}`);
    expect(invalidArgError7.cause).toEqual(InvalidArgumentError.defaultCause);

    const orderType = 'INVALID_ORDER';
    const invalidArgError8 = InvalidArgumentError.invalidOrderType({ context: errorDto3.context, target: orderType });

    expect(invalidArgError8).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError8.context).toEqual(errorDto3.context);
    expect(invalidArgError8.message).toEqual(`The order type ${orderType} is invalid`);
    expect(invalidArgError8.cause).toEqual(InvalidArgumentError.defaultCause);

    const disponibility = 'INVALID_DISP';
    const invalidArgError9 = InvalidArgumentError.invalidTicketTypeDisponibility({ context: errorDto3.context, target: disponibility });

    expect(invalidArgError9).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError9.context).toEqual(errorDto3.context);
    expect(invalidArgError9.message).toEqual(`The ticketType disponibility ${disponibility} is invalid`);
    expect(invalidArgError9.cause).toEqual(InvalidArgumentError.defaultCause);

    const rateType = 'INVALID_RATE';
    const invalidArgError10 = InvalidArgumentError.invalidGuestListRateType({ context: errorDto3.context, target: rateType });

    expect(invalidArgError10).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError10.context).toEqual(errorDto3.context);
    expect(invalidArgError10.message).toEqual(`The guestList rateType ${rateType} is invalid`);
    expect(invalidArgError10.cause).toEqual(InvalidArgumentError.defaultCause);

    const paylinkState = 'INVALID_STATE';
    const invalidArgError11 = InvalidArgumentError.invalidPaylinkState({ context: errorDto3.context, target: paylinkState });

    expect(invalidArgError11).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError11.context).toEqual(errorDto3.context);
    expect(invalidArgError11.message).toEqual(`The paylink state ${paylinkState} is invalid`);
    expect(invalidArgError11.cause).toEqual(InvalidArgumentError.defaultCause);

    const reservationState = 'INVALID_RES_STATE';
    const invalidArgError12 = InvalidArgumentError.invalidReservationState({ context: errorDto3.context, target: reservationState });

    expect(invalidArgError12).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError12.context).toEqual(errorDto3.context);
    expect(invalidArgError12.message).toEqual(`The reservation state ${reservationState} is invalid`);
    expect(invalidArgError12.cause).toEqual(InvalidArgumentError.defaultCause);

    const ticketState = 'INVALID_TICKET_STATE';
    const invalidArgError13 = InvalidArgumentError.invalidTicketState({ context: errorDto3.context, target: ticketState });

    expect(invalidArgError13).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError13.context).toEqual(errorDto3.context);
    expect(invalidArgError13.message).toEqual(`The ticket state ${ticketState} is invalid`);
    expect(invalidArgError13.cause).toEqual(InvalidArgumentError.defaultCause);

    const ticketRateType = 'INVALID_TICKET_RATE';
    const invalidArgError14 = InvalidArgumentError.invalidTicketTypeRateType({ context: errorDto3.context, target: ticketRateType });

    expect(invalidArgError14).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError14.context).toEqual(errorDto3.context);
    expect(invalidArgError14.message).toEqual(`The ticketType rateType ${ticketRateType} is invalid`);
    expect(invalidArgError14.cause).toEqual(InvalidArgumentError.defaultCause);

    const notificationType = 'INVALID_NOTIFICATION';
    const invalidArgError15 = InvalidArgumentError.invalidNotificationType({ context: errorDto3.context, target: notificationType });

    expect(invalidArgError15).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError15.context).toEqual(errorDto3.context);
    expect(invalidArgError15.message).toEqual(`The notification type ${notificationType} is invalid`);
    expect(invalidArgError15.cause).toEqual(InvalidArgumentError.defaultCause);

    const purchaseMin = '10';
    const invalidArgError16 = InvalidArgumentError.purchaseMinCannotBeGreaterThanMax({ context: errorDto3.context, target: purchaseMin });

    expect(invalidArgError16).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError16.context).toEqual(errorDto3.context);
    expect(invalidArgError16.message).toEqual(`The purchase min ${purchaseMin} cannot be greater than max`);
    expect(invalidArgError16.cause).toEqual(InvalidArgumentError.defaultCause);

    const unlimited = 'true';
    const invalidArgError17 = InvalidArgumentError.purchaseUnlimitedCannotBeTrueIfMaxIsProvided({ context: errorDto3.context, target: unlimited });

    expect(invalidArgError17).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError17.context).toEqual(errorDto3.context);
    expect(invalidArgError17.message).toEqual(`The purchase unlimited ${unlimited} cannot be true if max is provided`);
    expect(invalidArgError17.cause).toEqual(InvalidArgumentError.defaultCause);

    const quantity = '2';
    const invalidArgError18 = InvalidArgumentError.purchaseQuantityIsLessThanMin({ context: errorDto3.context, target: quantity });

    expect(invalidArgError18).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError18.context).toEqual(errorDto3.context);
    expect(invalidArgError18.message).toEqual(`The purchase quantity ${quantity} is less than min`);
    expect(invalidArgError18.cause).toEqual(InvalidArgumentError.defaultCause);

    const quantityMax = '20';
    const invalidArgError19 = InvalidArgumentError.purchaseQuantityIsGreaterThanMax({ context: errorDto3.context, target: quantityMax });

    expect(invalidArgError19).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError19.context).toEqual(errorDto3.context);
    expect(invalidArgError19.message).toEqual(`The purchase quantity ${quantityMax} is greater than max`);
    expect(invalidArgError19.cause).toEqual(InvalidArgumentError.defaultCause);

    const paymentType = 'INVALID_PAYMENT_TYPE';
    const invalidArgError20 = InvalidArgumentError.buildInvalidPaymentType({ context: errorDto3.context, target: paymentType });

    expect(invalidArgError20).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError20.context).toEqual(errorDto3.context);
    expect(invalidArgError20.message).toEqual(`The payment type ${paymentType} is invalid`);
    expect(invalidArgError20.cause).toEqual(InvalidArgumentError.defaultCause);

    const paymentMethod = 'INVALID_PAYMENT_METHOD';
    const invalidArgError21 = InvalidArgumentError.buildInvalidPaymentMethod({ context: errorDto3.context, target: paymentMethod });

    expect(invalidArgError21).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError21.context).toEqual(errorDto3.context);
    expect(invalidArgError21.message).toEqual(`The payment method ${paymentMethod} is invalid`);
    expect(invalidArgError21.cause).toEqual(InvalidArgumentError.defaultCause);

    const paymentState = 'INVALID_PAYMENT_STATE';
    const invalidArgError22 = InvalidArgumentError.buildInvalidPaymentState({ context: errorDto3.context, target: paymentState });

    expect(invalidArgError22).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError22.context).toEqual(errorDto3.context);
    expect(invalidArgError22.message).toEqual(`The payment state ${paymentState} is invalid`);
    expect(invalidArgError22.cause).toEqual(InvalidArgumentError.defaultCause);

    const paymentChannel = 'INVALID_PAYMENT_CHANNEL';
    const invalidArgError23 = InvalidArgumentError.buildInvalidPaymentChannel({ context: errorDto3.context, target: paymentChannel });

    expect(invalidArgError23).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError23.context).toEqual(errorDto3.context);
    expect(invalidArgError23.message).toEqual(`The payment channel ${paymentChannel} is invalid`);
    expect(invalidArgError23.cause).toEqual(InvalidArgumentError.defaultCause);

    const feeType = 'INVALID_FEE_TYPE';
    const invalidArgError24 = InvalidArgumentError.buildInvalidAdministrationFeeType({ context: errorDto3.context, target: feeType });

    expect(invalidArgError24).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError24.context).toEqual(errorDto3.context);
    expect(invalidArgError24.message).toEqual(`The administration fee type ${feeType} is invalid`);
    expect(invalidArgError24.cause).toEqual(InvalidArgumentError.defaultCause);

    const feeAmount = 10;
    const feeTypeForAmount = 'PERCENTAGE';
    const invalidArgError25 = InvalidArgumentError.buildInvalidAdministrationFeeAmount({
      context: errorDto3.context,
      target: feeAmount,
      data: { feeTypeForAmount },
    });

    expect(invalidArgError25).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError25.context).toEqual(errorDto3.context);
    expect(invalidArgError25.message).toEqual(`The administration fee amount ${feeAmount} is invalid for the type`);
    expect(invalidArgError25.cause).toEqual(InvalidArgumentError.defaultCause);
    expect(invalidArgError25.data).toStrictEqual({ feeTypeForAmount });

    const invalidResState = 'INVALID_RESERVATION_STATE';
    const invalidArgError26 = InvalidArgumentError.buildInvalidReservationState({ context: errorDto3.context, target: invalidResState });

    expect(invalidArgError26).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError26.context).toEqual(errorDto3.context);
    expect(invalidArgError26.message).toEqual(`The reservation state ${invalidResState} is invalid`);
    expect(invalidArgError26.cause).toEqual(InvalidArgumentError.defaultCause);

    const invalidValue = 'INVALID_VALUE';
    const invalidArgError27 = InvalidArgumentError.invalidValue({ context: errorDto4.context, target: invalidValue });

    expect(invalidArgError27).toBeInstanceOf(InvalidArgumentError);
    expect(invalidArgError27.context).toEqual(errorDto4.context);
    expect(invalidArgError27.message).toEqual(`Value "${invalidValue}" is invalid`);
    expect(invalidArgError27.cause).toEqual(InvalidArgumentError.defaultCause);
  });

  it('should return correct error for ImageError', () => {
    const dataForError = { value: 'image/jpeg' };
    const contextForError = 'MimeTypeValueObject';

    const imageError = ImageError.invalidFormat({ context: contextForError, data: dataForError });

    expect(imageError).toBeInstanceOf(ImageError);
    expect(imageError.context).toEqual(contextForError);
    expect(imageError.data).toStrictEqual(dataForError);
  });

  it('should return correct error for InvalidDateError', () => {
    const dataForError = { value: '01/02/3000' };
    const contextForError = 'FvDate';

    const imageError = InvalidDateError.build({ context: contextForError, data: dataForError });

    expect(imageError).toBeInstanceOf(InvalidDateError);
    expect(imageError.context).toEqual(contextForError);
    expect(imageError.data).toStrictEqual(dataForError);
  });

  it('should handle Dinero.js error when operating money with money', () => {
    const dinero1Dto = { amount: 1, currency: ECurrency.EUR };
    const dinero2Dto = { amount: 2, currency: ECurrency.USD };

    const dinero1OrError = Money.build(dinero1Dto);
    const dinero2OrError = Money.build(dinero2Dto);

    expect(dinero1OrError.isRight()).toBeTruthy();
    expect(dinero2OrError.isRight()).toBeTruthy();

    const dinero1 = dinero1OrError.value as Money;
    const dinero2 = dinero2OrError.value as Money;

    try {
      dinero1.add(dinero2);
    } catch (error) {
      const parsedError = error as MoneyError;

      const parsedErrorData = parsedError.data as {
        amount1: number;
        amount2: number;
        currency1: ECurrency;
        currency2: ECurrency;
      };

      expect(parsedError.message).toEqual('Currencies must match');

      expect(parsedErrorData.amount1).toStrictEqual(dinero1Dto.amount);
      expect(parsedErrorData.amount2).toStrictEqual(dinero2Dto.amount);

      expect(parsedErrorData.currency1).toStrictEqual(dinero1Dto.currency);
      expect(parsedErrorData.currency2).toStrictEqual(dinero2Dto.currency);
    }
  });

  it('should not be autoContextualizable', () => {
    const baseError = NotFoundError.build({ context: 'context' }).notAutoContextualizable();

    expect(baseError.isAutoContextualizable).toBe(false);
  });

  it('should handle native Error', () => {
    const nativeError1 = new Error('native_error_1');
    const nativeError2 = new Error('native_error_2');

    try {
      throw UnexpectedError.build({
        context: errorDto1.context,
        data: errorDto1.data,
        error: nativeError1,
      });
    } catch (error) {
      const parsedError = error as UnexpectedError;

      expect(parsedError).toBeInstanceOf(Error);
      expect(parsedError.context).toEqual(errorDto1.context);
      expect(parsedError.message).toEqual('Unexpected error');
      expect(parsedError.cause).toEqual('unexpected_error');
      expect(parsedError.data).toStrictEqual(errorDto1.data);

      const errorStack = parsedError.getStack();

      const error1Stack1 = errorStack[0] as FvErrorStack;

      expect(error1Stack1?.error).toEqual(nativeError1.toString());

      const contextualizedError = parsedError.contextualize({
        context: errorDto2.context,
        data: errorDto2.data,
        error: nativeError2,
      });

      expect(contextualizedError).toBeInstanceOf(UnexpectedError);
      expect(contextualizedError.context).toEqual(errorDto2.context);
      expect(contextualizedError.message).toEqual('Unexpected error');
      expect(contextualizedError.cause).toEqual('unexpected_error');
      expect(contextualizedError.data).toStrictEqual(errorDto2.data);

      const errorStack2 = contextualizedError.getStack();

      const error2Stack1 = errorStack2[0] as FvErrorStack;
      const error2Stack2 = errorStack2[1] as FvErrorStack;

      expect(error2Stack1.context).toEqual(errorDto2.context);
      expect(error2Stack2.context).toEqual(errorDto1.context);

      expect(error2Stack1.data).toStrictEqual(errorDto2.data);
      expect(error2Stack2.data).toStrictEqual(errorDto1.data);

      expect(error2Stack1.error).toEqual(nativeError2.toString());
      expect(error2Stack2.error).toEqual(nativeError1.toString());
    }
  });
});
