# Customized Fees Customers

Library with functions common to all Fourvenues.

## Installation

```bash
npm install --save @discocil/fv-domain-library
```

## Application layer
### Use cases
- **UseCase**

### Chain of responsibility
- **AbstractHandler**
- **Responsibility**

### Use case decorators
- **Decorator**
- **PerformanceMeasurementDecorator**

### ValidatorComposite
- **Component**
- **ValidatorComposite**

---

## Domain layer
### Collections
- **Collection**: class base for collections

### Value objects
- **ValueObject**: abstract class
- **FvBoolean**: operador de boleanos
- **FvNumber**: operador de numeros
- **FvString**: operador de cadenas
- **FvObject**: operador de objectos
- **FvDate**: operador de fechas
  - **DateEither** y **EFormats**: tipos de la clase
- **FvEnum**: abstract class
- **CreatedAt**, **RemovedAt** y **UpdatedAt**: operadores de timestamps
- **CreatedBy**, **RemovedBy** y **UpdatedBy**: operadores de timestamps
- **Gender**: maneja los diferentes generos
  - **EGender**, **EGenderName** y **EGenderGroup**: enums de la clase
- **ImageFile** y **MimeType**: operador de imagenes
- **Money**: operador de dinero
  - **MoneyEither** y **MoneyProps**: tipos de la clase
- **UniqueEntityID**: operador de identificadores unicos
- **UUID**: operador de uuids

- **PersonalDocument**: maneja los documentos de identidad de las personas
  - **PersonalDocumentPrimitives** y **EDocumentType**: tipos de la clase

- **PhoneNumber**: maneja los telefonos de las personas
  - **PhoneNumberPrimitives**: tipos de la clase

- **Pricing**: operea con precios fijos y variables
  - **PricingProps** y **PricingPrimitives**: tipos de la clase

### Errors
- **CheckError**
- **FvError**: abstract class base
  - **FvErrorRequest**, **FvErrorStack** y **ErrorContext**: tipos de la clase
- **ImageError**
- **InvalidArgumentError**
- **InvalidDateError**
- **NotFoundError**
- **UnexpectedError**
- **contextualizeError**: Decorador que contextualiza el error adhiriendole al mismo mas informacion

### Enums
- **EBillingAddressType**
- **EBookingCriteriaCompleteZone**
- **EBookingPaymentMethod**
- **EBookingPeople**
- **EBookingPricing**
- **EBookingSaleType**
- **EBookingSpaceTypes**
- **EBookingState**
- **EBookingTypeBailTypes**
- **EBookingTypeCoverChargeTypes**
- **ECountryCode**
- **ECurrency**
- **EDiscountCodeType**
- **EAtmosphere**
- **EEventConfigurationKeys**
- **EEventServices**
- **EFeeType**
- **EGGDD**
- **EHanger**
- **ImageSize**
- **ELanguagesCodes**
- **EMerchantNames**
  - **externalAdyenMerchantNames**
- **EMicrositeChannel**
- **EMicrositeServices**
- **EMimeType**
- **EMusicalGenres**
- **ENotificationType**
- **EOrderPaymentStates**
- **EOrderStates**
- **EOrganizationState**
- **EOrganizationType**
- **EArtistSource**
- **ELegacyPassState**
- **ENameChangeTypes**
- **EPassTypeSaleTypes**
- **EQuestionTypes**
- **EReceptionistTypes**
- **EResourceType**
- **EResourceTypes**
- **ESaleStates**
- **ESubscriptionPlanType**
- **ETicketRateTypes**
- **ETicketTypeDisponibilityTypes**
- **ETypeTicketLimit**
- **NotificationTypeEnum**
- **EPassState**
- **EPaylinkStatus**
- **EPaymentChannel**
- **EPaymentMethod**
- **EPaymentsProvider**
- **EPaymentStates**
- **EPaymentType**
- **EPaymentMethods**
- **EProvenance**
- **PostServiceStateTypes**
- **PreServiceStateTypes**
- **EReservationPaymentState**
- **EReservationState**
- **ServiceStateTypes**
- **EReservedReconfirmMethod**
- **EReservedReconfirmTime**
- **ESaleTypes**
- **ESupplements**
- **ETicketStates**
- **EGuestListState**
- **EGuestListSaleType**
- **EGuestListAcessState**
- **EGuestListPaymentMethod**
- **EGuestListShipment**
- **EGuestListRateTypes**
- **EPosMerchantName**

### Contracts
- **PassCodeGenerator**: contrato del servicio que genera el codigo único para los pases
- **TicketCodeGenerator**: contrato del servicio que genera el codigo único para las entradas
- **Logger**
- **LoggerStrategy**
- **LoggerMessage**
- **LoggerErrorMessage**

### Types
- **DatePrimitive**
- **IdPrimitive**

### Generics
- **CamelToSnake**
- **FlattenKeys**
- **Mutable**
- **NotEmptyArray**
- **Nullable**
- **Optional**
- **Partial**
- **Primitives**
- **Properties**
- **ReadonlyRecursive**
- **SnakeToCamel**
- **UnknownObject**
- **convertToJson**: funsion que convierte las primitivas de una entidad en un json valido
- **Serializable**: contratos que implementan .toPrimitives() y .toJson()
- **ToJson**: convierte un tipado Primitivo a Json
- **Maybe**
- **Either**, **left** y **right**

### Services
- **BinarySearch**: servicio para hacer busquedas de manera mas performante
- **CryptoService**: servicio que maneja crypto
- **PassCodeDatabaseGenerator**: servicio que genera el codigo único para los pases
- **PerformanceMeasurement**: servicio que mide la performance de un flujo
- **TicketCodeDatabaseGenerator**: servicio que genera el codigo único para las entradas

### Entities
- **AggregateRoot**
- **Entity**

### Domain events
- **DomainEvent**
- **DomainEventClass**
- **DomainEventRequest**
- **DomainEventSubscriber**

### Confis
- **DEFAULT_LENGUAGE**
- **countriesCodes**
- **countriesConfig**
- **provincesConfig**
- **ProvinceConfig (type)**
- **zipCodesConfig**
- **ZipCodeConfig (type)**

---

## Examples
### FvNumber
```typescript
const other = 5;
const value = FvNumber.build(10).add(other);

FvNumber.is('15'); // false
value.toPrimitive(); // 15
value.isEven(); // false
value.isGreaterThan(other); // true
value.isLessThanOrEqualTo(other); // false
value.isEqualTo(other); // false
value.isZero(); // false
value.isPositive(); // true
value.isNegative(); // false
```

### FvString
```typescript
const other = FvString.build('test-2');
const value = FvString.build('test');

value.isEmpty(); // false
value.toPrimitive(); // 'test'
value.isEqualTo(other); // false
value.isGreaterThan(other); // using localeCompare
value.removeSpaces();
value.normalize();
```

### FvDate
```typescript
const value = FvDate.create(); // or createFromSeconds, createFromMilliSeconds, createFromFormat
```

### Collection
```typescript
type ObjectItem = {
  readonly id: number;
  readonly name: string;
};

const numberCollection = Collection.new<number>();
const numberCollection2 = Collection.build<number>(Array.from({ length: 10 }, (_, i) => i));
const stringCollection = Collection.new<string>();
const objectCollection = Collection.new<ObjectItem>('id');

Array.from({ length: 10 }, (_, i) => {
  numberCollection.add(i);
  stringCollection.add(`Name-${i}`);
  objectCollection.add({ id: i, name: `Name-${i}` });
});

numberCollection.length(); // 10
numberCollection.isEmpty(); // false
numberCollection.isNotEmpty(); // true
numberCollection.add(11);
numberCollection.contains(20); // false
numberCollection.removeByIndex();
numberCollection.removeByValue();
numberCollection.getByIndex();
numberCollection.getByValue();
numberCollection.uniques(); // retrieve unique values
numberCollection.toArray();
numberCollection.merge(numberCollection2);
numberCollection.intersection(numberCollection2);
numberCollection.filter((item) => item > 2);
numberCollection.sort();
```

### Pass Code Generator
```typescript
const callback = (code: string): Promise<PassEither> => {
  const passRepository = container.resolve<IPassRepository>(PassDependencyIdentifier.IPassRepository);

  return passRepository.find(PassCriteriaMother.codeToMatch(code));
};

const passCodeGenerator = new PassCodeDatabaseGenerator(callback);

return await passCodeGenerator.execute();
```

### Ticket Code Generator
```typescript
const callback = (code: string): Promise<TicketEither> => {
  const ticketRepository = container.resolve<ITicketRepository>(TicketDependencyIdentifier.ITicketRepository);

  return ticketRepository.find(TicketCriteriaMother.codeToMatch(code));
};

const ticketCodeGenerator = new TicketCodeDatabaseGenerator(callback);

return await ticketCodeGenerator.execute(event.code);
```

### Pricing
```typescript
const pricing = Pricing.build(ECustomFeeTypeCalculation.PERCENTAGE, {
  amount: 10,
  currency: 'EUR'
}).value!;

const price = Money.build({ amount: 100, currency: 'EUR' }).value;

const summary = pricing.calculate(price);

// summary.total = 110
// summary.calculated = 10
// summary.base = 100
```

### FvError
```typescript
// Build error
const myError = NotFoundError.build({
  context: this.constructor.name, // name of useCase, service, repository...
  target: EventEntity.name, // target of error
  data: { id: 1 },
});

// Give more context when the error is handled by parent function
return left(useCaseResult.value.contextualize({
  context: this.constructor.name, // MyUseCaseName
  data: { dto },
}))
```

---

## Releasing Changes
When you want to push changes to this package, you should run the following command instead of using `git push`:

```bash
npm run release
```

This command will handle versioning, tagging, and pushing the changes to the repository.

## Performance testing on search types
| numberOfRecord | target                               | binarySearch        | collection->getByIndex | collection->getByValue | array->findIndex   | array->find       |
| -------------: | :----------------------------------- | :------------------ | :--------------------- | :--------------------- | :----------------- | :---------------- |
| 1.000.000      | 999999                               | 1.0625829999999041  | 0.058958999999958905   | 0.011209000000008018   | 4.393332999999984  | 4.635833000000048 |
| 1.000.000      | 'Name-999999'                        | 0.2748750000000655  | 0.005667000000130429   | 0.002207999999882304   | 6.647707999999966  | 6.069582999999966 |
| 1.000.000      | { id: 999999, name: 'Name-999999' }  | 0.16070799999988594 | 0.006624999999985448   | 0.0027500000001055014  | 4.94666600000005   | 4.855125000000044 |


## Author
This package is maintained by the Fourvenues team.

## Project Status
This project is actively maintained. Contributions and feedback are welcome.
